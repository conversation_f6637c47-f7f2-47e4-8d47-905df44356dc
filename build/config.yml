# This file contains the configuration for this project.
# When you update `info` or `fileAssociations`, run `wails3 task common:update:build-assets` to update the assets.
# Note that this will overwrite any changes you have made to the assets.
version: '3'

# This information is used to generate the build assets.
info:
  companyName: "My Company" # The name of the company
  productName: "My Product" # The name of the application
  productIdentifier: "com.mycompany.myproduct" # The unique product identifier
  description: "A program that does X" # The application description
  copyright: "(c) 2025, My Company" # Copyright text
  comments: "Some Product Comments" # Comments
  version: "0.0.1" # The application version

# Dev mode configuration
dev_mode:
  root_path: .
  log_level: warn
  debounce: 1000
  ignore:
    dir:
      - .git
      - node_modules
      - frontend
      - bin
    file:
      - .DS_Store
      - .gitignore
      - .gitkeep
    watched_extension:
      - "*.go"
    git_ignore: true
  executes:
    - cmd: wails3 task common:install:frontend:deps
      type: once
    - cmd: wails3 task common:dev:frontend
      type: background
    - cmd: go mod tidy
      type: blocking
    - cmd: wails3 task build
      type: blocking
    - cmd: wails3 task run
      type: primary

# File Associations
# More information at: https://v3.wails.io/noit/done/yet
fileAssociations:
#  - ext: wails
#    name: Wails
#    description: Wails Application File
#    iconName: wailsFileIcon
#    role: Editor
#  - ext: jpg
#    name: JPEG
#    description: Image File
#    iconName: jpegFileIcon
#    role: Editor
#    mimeType: image/jpeg  # (optional)

# Other data
other:
  - name: My Other Data