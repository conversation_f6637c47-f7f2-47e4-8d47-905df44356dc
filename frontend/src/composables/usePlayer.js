import { ref, computed } from 'vue'
import { Howl } from 'howler'

// 全局播放器状态
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(0.7)
const isMuted = ref(false)
const isShuffled = ref(false)
const repeatMode = ref('none') // 'none', 'all', 'one'

const currentTrack = ref(null)
const currentSound = ref(null)
const playlist = ref([])
const currentIndex = ref(0)

// 播放器管理 composable
export function usePlayer() {
  
  // 计算属性
  const progressPercentage = computed(() => {
    if (duration.value === 0) return 0
    return (currentTime.value / duration.value) * 100
  })

  const hasNextTrack = computed(() => {
    return currentIndex.value < playlist.value.length - 1
  })

  const hasPreviousTrack = computed(() => {
    return currentIndex.value > 0
  })

  // 加载音轨
  const loadTrack = (track, index = null) => {
    // 清理之前的音频实例
    if (currentSound.value) {
      currentSound.value.stop()
      currentSound.value.unload()
      currentSound.value = null
    }

    // 重置状态
    isPlaying.value = false
    currentTime.value = 0
    duration.value = 0

    currentTrack.value = track
    if (index !== null) {
      currentIndex.value = index
    }

    console.log('Loading track:', track.title, 'URL:', track.url)

    currentSound.value = new Howl({
      src: [track.url],
      volume: isMuted.value ? 0 : volume.value,
      html5: true, // 强制使用 HTML5 Audio，对大文件更友好
      preload: true, // 预加载音频
      onload: () => {
        console.log('Audio loaded successfully')
        duration.value = currentSound.value.duration()
        if (duration.value === 0) {
          console.warn('Duration is 0, audio may not be loaded properly')
        }
      },
      onplay: () => {
        console.log('Audio started playing')
        isPlaying.value = true
        updateProgress()
      },
      onpause: () => {
        console.log('Audio paused')
        isPlaying.value = false
      },
      onend: () => {
        console.log('Audio ended')
        isPlaying.value = false
        currentTime.value = 0
        handleTrackEnd()
      },
      onloaderror: (id, error) => {
        console.error('Failed to load audio:', error)
        console.error('Audio URL:', track.url)
        // 检查文件是否存在
        fetch(track.url)
          .then(response => {
            if (!response.ok) {
              console.error('Audio file not found:', response.status, response.statusText)
            }
          })
          .catch(fetchError => {
            console.error('Network error when checking audio file:', fetchError)
          })

        // 尝试播放下一首
        if (hasNextTrack.value) {
          setTimeout(() => nextTrack(), 1000) // 延迟1秒后尝试下一首
        }
      },
      onplayerror: (id, error) => {
        console.error('Playback error:', error)
        isPlaying.value = false
      }
    })
  }

  // 播放/暂停
  const togglePlayPause = () => {
    if (!currentSound.value) {
      // 如果没有当前音轨，加载播放列表的第一首
      if (playlist.value.length > 0) {
        loadTrack(playlist.value[0], 0)
        // 等待加载完成后播放
        setTimeout(() => {
          if (currentSound.value && currentSound.value.state() === 'loaded') {
            currentSound.value.play()
          }
        }, 100)
      }
      return
    }

    // 检查音频状态
    if (currentSound.value.state() !== 'loaded') {
      console.warn('Audio not loaded yet, state:', currentSound.value.state())
      return
    }

    if (isPlaying.value) {
      currentSound.value.pause()
    } else {
      try {
        currentSound.value.play()
      } catch (error) {
        console.error('Error playing audio:', error)
        isPlaying.value = false
      }
    }
  }

  // 上一首
  const previousTrack = () => {
    if (!playlist.value.length) return
    
    let newIndex
    if (isShuffled.value) {
      // 随机模式：随机选择一首（排除当前）
      const availableIndices = playlist.value
        .map((_, index) => index)
        .filter(index => index !== currentIndex.value)
      newIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)]
    } else {
      // 顺序模式
      newIndex = currentIndex.value > 0 ? currentIndex.value - 1 : playlist.value.length - 1
    }
    
    loadTrack(playlist.value[newIndex], newIndex)
    if (isPlaying.value) {
      currentSound.value.play()
    }
  }

  // 下一首
  const nextTrack = () => {
    if (!playlist.value.length) return
    
    let newIndex
    if (isShuffled.value) {
      // 随机模式：随机选择一首（排除当前）
      const availableIndices = playlist.value
        .map((_, index) => index)
        .filter(index => index !== currentIndex.value)
      newIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)]
    } else {
      // 顺序模式
      newIndex = currentIndex.value < playlist.value.length - 1 ? currentIndex.value + 1 : 0
    }
    
    loadTrack(playlist.value[newIndex], newIndex)
    if (isPlaying.value) {
      currentSound.value.play()
    }
  }

  // 随机播放切换
  const toggleShuffle = () => {
    isShuffled.value = !isShuffled.value
  }

  // 重复模式切换
  const toggleRepeat = () => {
    const modes = ['none', 'all', 'one']
    const currentModeIndex = modes.indexOf(repeatMode.value)
    repeatMode.value = modes[(currentModeIndex + 1) % modes.length]
  }

  // 静音切换
  const toggleMute = () => {
    isMuted.value = !isMuted.value
    if (currentSound.value) {
      currentSound.value.mute(isMuted.value)
    }
  }

  // 更新音量
  const updateVolume = (newVolume) => {
    const vol = parseFloat(newVolume)
    if (isNaN(vol) || vol < 0 || vol > 1) {
      console.warn('Invalid volume value:', newVolume)
      return
    }

    volume.value = vol
    if (currentSound.value) {
      currentSound.value.volume(isMuted.value ? 0 : vol)
    }
    if (vol > 0) {
      isMuted.value = false
    }
  }

  // 跳转到指定时间
  const seekTo = (time) => {
    if (!currentSound.value) return

    const seekTime = parseFloat(time)
    if (isNaN(seekTime) || seekTime < 0) {
      console.warn('Invalid seek time:', time)
      return
    }

    // 确保不超过音频长度
    const maxTime = duration.value || 0
    const clampedTime = Math.min(seekTime, maxTime)

    try {
      currentSound.value.seek(clampedTime)
      currentTime.value = clampedTime
    } catch (error) {
      console.error('Error seeking audio:', error)
    }
  }

  // 更新进度
  const updateProgress = () => {
    if (isPlaying.value && currentSound.value && currentSound.value.playing()) {
      const seek = currentSound.value.seek()
      if (typeof seek === 'number' && !isNaN(seek)) {
        currentTime.value = seek
      }
      requestAnimationFrame(updateProgress)
    }
  }

  // 处理音轨结束
  const handleTrackEnd = () => {
    if (repeatMode.value === 'one') {
      // 单曲循环
      currentSound.value.play()
    } else if (repeatMode.value === 'all' || hasNextTrack.value) {
      // 列表循环或有下一首
      nextTrack()
    } else {
      // 播放结束
      currentTime.value = 0
    }
  }

  // 设置播放列表
  const setPlaylist = (tracks) => {
    playlist.value = tracks
    currentIndex.value = 0
    // 自动加载第一首歌（但不播放）
    if (tracks.length > 0) {
      loadTrack(tracks[0], 0)
    }
  }

  // 添加到播放列表
  const addToPlaylist = (track) => {
    playlist.value.push(track)
  }

  // 从播放列表移除
  const removeFromPlaylist = (index) => {
    if (index === currentIndex.value) {
      // 如果移除的是当前播放的音轨
      if (currentSound.value) {
        currentSound.value.unload()
      }
      currentTrack.value = null
      currentSound.value = null
    } else if (index < currentIndex.value) {
      // 如果移除的音轨在当前播放音轨之前，调整索引
      currentIndex.value--
    }
    
    playlist.value.splice(index, 1)
  }

  // 播放指定音轨
  const playTrack = (track, index) => {
    loadTrack(track, index)
    currentSound.value.play()
  }

  // 格式化时间
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 获取重复模式标题
  const getRepeatTitle = () => {
    switch (repeatMode.value) {
      case 'one': return 'Repeat One'
      case 'all': return 'Repeat All'
      default: return 'Repeat Off'
    }
  }

  // 检查音频状态
  const getAudioState = () => {
    if (!currentSound.value) return 'no-audio'
    return currentSound.value.state()
  }

  // 检查音频是否可以播放
  const canPlay = () => {
    return currentSound.value && currentSound.value.state() === 'loaded'
  }

  // 清理资源
  const cleanup = () => {
    if (currentSound.value) {
      currentSound.value.stop()
      currentSound.value.unload()
      currentSound.value = null
    }
    isPlaying.value = false
    currentTime.value = 0
    duration.value = 0
  }

  return {
    // 状态
    isPlaying,
    currentTime,
    duration,
    volume,
    isMuted,
    isShuffled,
    repeatMode,
    currentTrack,
    playlist,
    currentIndex,
    
    // 计算属性
    progressPercentage,
    hasNextTrack,
    hasPreviousTrack,
    
    // 方法
    loadTrack,
    togglePlayPause,
    previousTrack,
    nextTrack,
    toggleShuffle,
    toggleRepeat,
    toggleMute,
    updateVolume,
    seekTo,
    setPlaylist,
    addToPlaylist,
    removeFromPlaylist,
    playTrack,
    formatTime,
    getRepeatTitle,
    getAudioState,
    canPlay,
    cleanup
  }
}
