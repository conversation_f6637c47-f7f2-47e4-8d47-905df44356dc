{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --minify false --mode development", "build": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@fluentui/web-components": "^2.6.1", "@wailsio/runtime": "latest", "howler": "^2.2.4", "vue": "^3.5.18"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "vite": "^5.0.0"}}