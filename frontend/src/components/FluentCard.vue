<template>
  <div 
    class="fluent-card-component"
    :class="{ 
      'card-hover': hover,
      'card-clickable': clickable,
      'card-elevated': elevated 
    }"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
    @mousemove="onMouseMove"
    @click="onClick"
  >
    <div v-if="title || $slots.header" class="card-header">
      <slot name="header">
        <h3 class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </slot>
    </div>
    
    <div class="card-content">
      <slot></slot>
    </div>
    
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  hover: {
    type: Boolean,
    default: true
  },
  clickable: {
    type: Boolean,
    default: false
  },
  elevated: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const cardRef = ref(null)

const onMouseEnter = () => {
  if (!props.hover) return
}

const onMouseLeave = () => {
  if (!props.hover) return
}

const onMouseMove = (event) => {
  if (!props.hover) return
  
  const card = event.currentTarget
  const rect = card.getBoundingClientRect()
  const x = ((event.clientX - rect.left) / rect.width) * 100
  const y = ((event.clientY - rect.top) / rect.height) * 100
  
  card.style.setProperty('--mouse-x', `${x}%`)
  card.style.setProperty('--mouse-y', `${y}%`)
}

const onClick = (event) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped>
.fluent-card-component {
  background-color: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  box-shadow: var(--fluent-shadow-2);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  position: relative;
  overflow: hidden;
}

.fluent-card-component.card-elevated {
  box-shadow: var(--fluent-shadow-8);
}

.fluent-card-component.card-hover:hover {
  box-shadow: var(--fluent-shadow-4);
  border-color: var(--fluent-border-hover);
  transform: translateY(-1px);
}

.fluent-card-component.card-elevated.card-hover:hover {
  box-shadow: var(--fluent-shadow-16);
}

.fluent-card-component.card-clickable {
  cursor: pointer;
}

.fluent-card-component.card-clickable:active {
  transform: translateY(0);
  box-shadow: var(--fluent-shadow-2);
}

/* Reveal effect */
.fluent-card-component.card-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at var(--mouse-x, 50%) var(--mouse-y, 50%), 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  pointer-events: none;
}

.fluent-card-component.card-hover:hover::before {
  opacity: 1;
}

@media (prefers-color-scheme: dark) {
  .fluent-card-component.card-hover::before {
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%), 
      rgba(255, 255, 255, 0.05) 0%, 
      transparent 50%
    );
  }
}

.card-header {
  padding: var(--fluent-spacing-l) var(--fluent-spacing-l) 0;
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: var(--fluent-font-size-400);
  font-weight: 600;
  color: var(--fluent-text-primary);
  margin: 0 0 var(--fluent-spacing-xs) 0;
}

.card-subtitle {
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-text-secondary);
  margin: 0;
}

.card-content {
  padding: var(--fluent-spacing-l);
  position: relative;
  z-index: 1;
}

.card-footer {
  padding: 0 var(--fluent-spacing-l) var(--fluent-spacing-l);
  position: relative;
  z-index: 1;
}
</style>
