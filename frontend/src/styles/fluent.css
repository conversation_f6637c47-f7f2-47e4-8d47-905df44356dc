/* Fluent Design System Variables */
:root {
  /* Colors */
  --fluent-primary: #0078d4;
  --fluent-primary-hover: #106ebe;
  --fluent-primary-pressed: #005a9e;
  
  --fluent-neutral-background: #f3f2f1;
  --fluent-neutral-background-hover: #edebe9;
  --fluent-neutral-background-pressed: #e1dfdd;
  
  --fluent-card-background: #ffffff;
  --fluent-card-background-secondary: #faf9f8;
  
  --fluent-text-primary: #323130;
  --fluent-text-secondary: #605e5c;
  --fluent-text-disabled: #a19f9d;
  
  --fluent-border: #edebe9;
  --fluent-border-hover: #d2d0ce;
  
  /* Shadows */
  --fluent-shadow-2: 0 1px 2px rgba(0, 0, 0, 0.14), 0 0px 2px rgba(0, 0, 0, 0.12);
  --fluent-shadow-4: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0px 2px rgba(0, 0, 0, 0.12);
  --fluent-shadow-8: 0 4px 8px rgba(0, 0, 0, 0.14), 0 0px 2px rgba(0, 0, 0, 0.12);
  --fluent-shadow-16: 0 8px 16px rgba(0, 0, 0, 0.14), 0 0px 2px rgba(0, 0, 0, 0.12);
  
  /* Border Radius */
  --fluent-corner-radius-small: 2px;
  --fluent-corner-radius-medium: 4px;
  --fluent-corner-radius-large: 8px;
  
  /* Typography */
  --fluent-font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  --fluent-font-size-100: 12px;
  --fluent-font-size-200: 14px;
  --fluent-font-size-300: 16px;
  --fluent-font-size-400: 18px;
  --fluent-font-size-500: 20px;
  --fluent-font-size-600: 24px;
  
  /* Spacing */
  --fluent-spacing-xs: 4px;
  --fluent-spacing-s: 8px;
  --fluent-spacing-m: 12px;
  --fluent-spacing-l: 16px;
  --fluent-spacing-xl: 20px;
  --fluent-spacing-xxl: 24px;
  
  /* Animation */
  --fluent-duration-ultra-fast: 50ms;
  --fluent-duration-faster: 100ms;
  --fluent-duration-fast: 150ms;
  --fluent-duration-normal: 200ms;
  --fluent-duration-slow: 300ms;
  --fluent-duration-slower: 400ms;
  
  --fluent-curve-accelerate-max: cubic-bezier(1, 0, 1, 1);
  --fluent-curve-accelerate-mid: cubic-bezier(0.7, 0, 1, 0.5);
  --fluent-curve-decelerate-max: cubic-bezier(0, 0, 0, 1);
  --fluent-curve-decelerate-mid: cubic-bezier(0.1, 0.9, 0.2, 1);
  --fluent-curve-easy-ease: cubic-bezier(0.33, 0, 0.67, 1);
  --fluent-curve-linear: cubic-bezier(0, 0, 1, 1);
}

/* Dark theme */
.dark-theme {
  --fluent-neutral-background: #201f1e;
  --fluent-neutral-background-hover: #292827;
  --fluent-neutral-background-pressed: #323130;

  --fluent-card-background: #292827;
  --fluent-card-background-secondary: #323130;

  --fluent-text-primary: #ffffff;
  --fluent-text-secondary: #d2d0ce;
  --fluent-text-disabled: #605e5c;

  --fluent-border: #323130;
  --fluent-border-hover: #484644;
}

/* Light theme */
.light-theme {
  --fluent-neutral-background: #f3f2f1;
  --fluent-neutral-background-hover: #edebe9;
  --fluent-neutral-background-pressed: #e1dfdd;

  --fluent-card-background: #ffffff;
  --fluent-card-background-secondary: #faf9f8;

  --fluent-text-primary: #323130;
  --fluent-text-secondary: #605e5c;
  --fluent-text-disabled: #a19f9d;

  --fluent-border: #edebe9;
  --fluent-border-hover: #d2d0ce;
}

/* System theme fallback */
@media (prefers-color-scheme: dark) {
  :root:not(.light-theme):not(.dark-theme) {
    --fluent-neutral-background: #201f1e;
    --fluent-neutral-background-hover: #292827;
    --fluent-neutral-background-pressed: #323130;

    --fluent-card-background: #292827;
    --fluent-card-background-secondary: #323130;

    --fluent-text-primary: #ffffff;
    --fluent-text-secondary: #d2d0ce;
    --fluent-text-disabled: #605e5c;

    --fluent-border: #323130;
    --fluent-border-hover: #484644;
  }
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--fluent-font-family);
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-text-primary);
  background-color: var(--fluent-neutral-background);
  margin: 0;
  padding: 0;
  line-height: 1.4;
  overflow: hidden; /* Prevent scrollbars on frameless window */
}

/* Frameless window support */
html, body, #app {
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#app {
  display: flex;
  flex-direction: column;
}

/* Disable text selection for better drag experience */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Custom scrollbar for frameless window */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--fluent-neutral-background);
}

::-webkit-scrollbar-thumb {
  background: var(--fluent-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--fluent-border-hover);
}

/* Dark theme scrollbar */
.dark-theme ::-webkit-scrollbar-track {
  background: var(--fluent-neutral-background);
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: var(--fluent-border);
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--fluent-border-hover);
}

/* Fluent Card */
.fluent-card {
  background-color: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  box-shadow: var(--fluent-shadow-2);
  padding: var(--fluent-spacing-l);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.fluent-card:hover {
  box-shadow: var(--fluent-shadow-4);
  border-color: var(--fluent-border-hover);
}

/* Fluent Button */
.fluent-button {
  font-family: var(--fluent-font-family);
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-medium);
  padding: var(--fluent-spacing-s) var(--fluent-spacing-l);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  outline: none;
  position: relative;
  overflow: hidden;
}

.fluent-button-primary {
  background-color: var(--fluent-primary);
  border-color: var(--fluent-primary);
  color: white;
}

.fluent-button-primary:hover {
  background-color: var(--fluent-primary-hover);
  border-color: var(--fluent-primary-hover);
}

.fluent-button-primary:active {
  background-color: var(--fluent-primary-pressed);
  border-color: var(--fluent-primary-pressed);
}

.fluent-button-secondary {
  background-color: var(--fluent-card-background);
  border-color: var(--fluent-border);
  color: var(--fluent-text-primary);
}

.fluent-button-secondary:hover {
  background-color: var(--fluent-neutral-background-hover);
  border-color: var(--fluent-border-hover);
}

/* Fluent Input */
.fluent-input {
  font-family: var(--fluent-font-family);
  font-size: var(--fluent-font-size-200);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-medium);
  padding: var(--fluent-spacing-s) var(--fluent-spacing-m);
  background-color: var(--fluent-card-background);
  color: var(--fluent-text-primary);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  outline: none;
}

.fluent-input:focus {
  border-color: var(--fluent-primary);
  box-shadow: 0 0 0 1px var(--fluent-primary);
}

.fluent-input::placeholder {
  color: var(--fluent-text-secondary);
}

/* Fluent Navigation */
.fluent-nav {
  background-color: var(--fluent-card-background);
  border-right: 1px solid var(--fluent-border);
  padding: var(--fluent-spacing-l);
  min-height: 100vh;
}

.fluent-nav-item {
  display: flex;
  align-items: center;
  padding: var(--fluent-spacing-s) var(--fluent-spacing-m);
  border-radius: var(--fluent-corner-radius-medium);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  margin-bottom: var(--fluent-spacing-xs);
}

.fluent-nav-item:hover {
  background-color: var(--fluent-neutral-background-hover);
}

.fluent-nav-item.active {
  background-color: var(--fluent-primary);
  color: white;
}

/* Fluent Typography */
.fluent-title-1 {
  font-size: var(--fluent-font-size-600);
  font-weight: 600;
  margin: 0 0 var(--fluent-spacing-l) 0;
}

.fluent-title-2 {
  font-size: var(--fluent-font-size-500);
  font-weight: 600;
  margin: 0 0 var(--fluent-spacing-m) 0;
}

.fluent-title-3 {
  font-size: var(--fluent-font-size-400);
  font-weight: 600;
  margin: 0 0 var(--fluent-spacing-s) 0;
}

.fluent-body {
  font-size: var(--fluent-font-size-200);
  line-height: 1.4;
  margin: 0 0 var(--fluent-spacing-m) 0;
}

.fluent-caption {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  margin: 0;
}

/* Acrylic effect */
.fluent-acrylic {
  backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  .fluent-acrylic {
    background-color: rgba(32, 31, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Reveal effect */
.fluent-reveal {
  position: relative;
  overflow: hidden;
}

.fluent-reveal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), 
              rgba(255, 255, 255, 0.1) 0%, 
              transparent 50%);
  opacity: 0;
  transition: opacity var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  pointer-events: none;
}

.fluent-reveal:hover::before {
  opacity: 1;
}

/* Responsive Design for 1600x900 */
@media (min-width: 1600px) {
  :root {
    /* Larger spacing for bigger screens */
    --fluent-spacing-xs: 6px;
    --fluent-spacing-s: 10px;
    --fluent-spacing-m: 16px;
    --fluent-spacing-l: 20px;
    --fluent-spacing-xl: 24px;
    --fluent-spacing-xxl: 32px;

    /* Larger font sizes */
    --fluent-font-size-100: 13px;
    --fluent-font-size-200: 15px;
    --fluent-font-size-300: 17px;
    --fluent-font-size-400: 20px;
    --fluent-font-size-500: 22px;
    --fluent-font-size-600: 28px;
  }

  /* Navigation adjustments */
  .fluent-nav {
    min-width: 320px;
    padding: var(--fluent-spacing-xl);
  }

  /* Card grid optimizations */
  .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--fluent-spacing-xl);
  }

  /* Content area padding */
  .content-area {
    padding: var(--fluent-spacing-xxl) var(--fluent-spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .fluent-card {
    border-width: 0.5px;
  }

  .fluent-button {
    border-width: 0.5px;
  }

  .fluent-input {
    border-width: 0.5px;
  }
}

/* Smooth theme transitions */
* {
  transition: background-color var(--fluent-duration-normal) var(--fluent-curve-easy-ease),
              border-color var(--fluent-duration-normal) var(--fluent-curve-easy-ease),
              color var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

/* Focus management for accessibility */
.fluent-button:focus-visible,
.fluent-input:focus-visible,
.fluent-nav-item:focus-visible {
  outline: 2px solid var(--fluent-primary);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .fluent-reveal::before {
    display: none;
  }
}

/* Window control specific styles */
.window-controls {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* Platform-specific title bar adjustments */
@media (platform: windows) {
  .title-bar {
    height: 32px;
  }

  .window-button {
    width: 46px;
    height: 32px;
    border-radius: 0;
  }

  .window-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .close-button:hover {
    background-color: #e81123 !important;
  }
}

@media (platform: macos) {
  .title-bar {
    padding-left: 80px;
    height: 40px;
  }

  .window-controls {
    display: none; /* Hide on macOS, use native traffic lights */
  }
}

@media (platform: linux) {
  .title-bar {
    height: 40px;
  }

  .window-button {
    width: 32px;
    height: 32px;
  }
}

/* Ensure proper layering */
.title-bar {
  z-index: 1000;
  position: relative;
}

/* Prevent interference with drag */
.title-bar button,
.title-bar input,
.title-bar select {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}
