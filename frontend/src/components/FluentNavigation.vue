<template>
  <nav class="fluent-navigation">
    <div class="nav-content">
      <div class="nav-section">
        <div class="section-header">Main</div>
        <div 
          v-for="item in mainItems" 
          :key="item.id"
          class="fluent-nav-item"
          :class="{ active: activeItem === item.id }"
          @click="selectItem(item.id)"
        >
          <span class="nav-icon" v-html="item.icon"></span>
          <span class="nav-text">{{ item.text }}</span>
        </div>
      </div>
      
      <div class="nav-section">
        <div class="section-header">Tools</div>
        <div 
          v-for="item in toolItems" 
          :key="item.id"
          class="fluent-nav-item"
          :class="{ active: activeItem === item.id }"
          @click="selectItem(item.id)"
        >
          <span class="nav-icon" v-html="item.icon"></span>
          <span class="nav-text">{{ item.text }}</span>
        </div>
      </div>
    </div>
    
    <div class="nav-footer">
      <div class="user-info">
        <div class="user-avatar">
          <span>U</span>
        </div>
        <div class="user-details">
          <div class="user-name">User</div>
          <div class="user-status">Online</div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['navigate'])

const activeItem = ref('home')

const mainItems = [
  {
    id: 'home',
    text: 'Home',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
             <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
           </svg>`
  },
  {
    id: 'greet',
    text: 'Greet Service',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
             <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z"/>
           </svg>`
  },
  {
    id: 'media',
    text: 'Media Player',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
             <path d="m11.596 8.697-6.363 3.692c-.54.313-1.233-.066-1.233-.697V4.308c0-.63.692-1.01 1.233-.696l6.363 3.692a.802.802 0 0 1 0 1.393z"/>
           </svg>`
  }
]

const toolItems = [
  {
    id: 'settings',
    text: 'Settings',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
             <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
             <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.292-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.292c.415.764-.42 1.6-1.185 1.184l-.292-.159a1.873 1.873 0 0 0-2.692 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.693-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.292A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/>
           </svg>`
  },
  {
    id: 'about',
    text: 'About',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
             <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
           </svg>`
  }
]

const selectItem = (itemId) => {
  activeItem.value = itemId
  emit('navigate', itemId)
}
</script>

<style scoped>
.fluent-navigation {
  width: 280px;
  background-color: var(--fluent-neutral-background);
  border-right: 1px solid var(--fluent-border);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}



.nav-content {
  flex: 1;
  padding: var(--fluent-spacing-l);
  padding-top: var(--fluent-spacing-xl); /* 增加顶部间距 */
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--fluent-spacing-xl);
}

.section-header {
  font-size: var(--fluent-font-size-100);
  font-weight: 600;
  color: var(--fluent-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--fluent-spacing-s);
  padding: 0 var(--fluent-spacing-m);
}

.fluent-nav-item {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
  padding: var(--fluent-spacing-s) var(--fluent-spacing-m);
  border-radius: var(--fluent-corner-radius-medium);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  margin-bottom: var(--fluent-spacing-xs);
  color: var(--fluent-text-primary);
}

.fluent-nav-item:hover {
  background-color: var(--fluent-neutral-background-hover);
}

.fluent-nav-item.active {
  background-color: var(--fluent-primary);
  color: white;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.nav-text {
  font-size: var(--fluent-font-size-200);
  font-weight: 400;
}

.nav-footer {
  padding: var(--fluent-spacing-l);
  border-top: 1px solid var(--fluent-border);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--fluent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: var(--fluent-font-size-200);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  color: var(--fluent-text-primary);
}

.user-status {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
}
</style>
