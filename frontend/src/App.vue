<template>
  <div class="app-container">
    <!-- Custom Title Bar -->
    <TitleBar />

    <div class="app-body">
      <FluentNavigation @navigate="handleNavigation" />

      <main class="main-content">
        <div class="content-area">
        <!-- Home View -->
        <div v-if="currentView === 'home'" class="view-container">
          <div class="page-header">
            <h1 class="fluent-title-1">Welcome to GMPlayer</h1>
            <p class="fluent-body">A modern Wails application with Fluent Design</p>
          </div>

          <div class="cards-grid">
            <FluentCard
              title="Quick Start"
              subtitle="Get started with the application"
              :hover="true"
              :clickable="true"
              @click="currentView = 'greet'"
            >
              <p>Try out the greeting service to see the app in action.</p>
              <template #footer>
                <button class="fluent-button fluent-button-primary">
                  Get Started
                </button>
              </template>
            </FluentCard>

            <FluentCard
              title="System Info"
              subtitle="Application details"
              :hover="true"
            >
              <div class="info-item">
                <span class="info-label">Framework:</span>
                <span class="info-value">Wails v3 + Vue 3</span>
              </div>
              <div class="info-item">
                <span class="info-label">Design:</span>
                <span class="info-value">Fluent Design System</span>
              </div>
              <div class="info-item">
                <span class="info-label">Status:</span>
                <span class="info-value">{{ timeDisplay }}</span>
              </div>
            </FluentCard>
          </div>
        </div>

        <!-- Greet Service View -->
        <div v-else-if="currentView === 'greet'" class="view-container">
          <div class="page-header">
            <h1 class="fluent-title-1">Greeting Service</h1>
            <p class="fluent-body">Test the backend service integration</p>
          </div>

          <FluentCard
            title="Send Greeting"
            subtitle="Enter your name to receive a personalized greeting"
            :elevated="true"
          >
            <div class="greeting-form">
              <div class="form-group">
                <label class="form-label">Your Name</label>
                <input
                  class="fluent-input"
                  v-model="name"
                  type="text"
                  autocomplete="off"
                  @keyup.enter="doGreet"
                  placeholder="Enter your name"
                />
              </div>

              <div class="form-actions">
                <button
                  class="fluent-button fluent-button-primary"
                  @click="doGreet"
                  :disabled="!name.trim()"
                >
                  Send Greeting
                </button>
                <button
                  class="fluent-button fluent-button-secondary"
                  @click="clearForm"
                >
                  Clear
                </button>
              </div>
            </div>

            <div v-if="result" class="result-section">
              <div class="result-label">Response:</div>
              <div class="result-text">{{ result }}</div>
            </div>
          </FluentCard>
        </div>

        <!-- Other Views -->
        <div v-else class="view-container">
          <div class="page-header">
            <h1 class="fluent-title-1">{{ getViewTitle() }}</h1>
            <p class="fluent-body">This section is under development</p>
          </div>

          <FluentCard title="Coming Soon" subtitle="This feature will be available in a future update">
            <p>We're working hard to bring you more features. Stay tuned!</p>
          </FluentCard>
        </div>
      </div>
    </main>
    </div>

    <!-- Player Bar -->
    <PlayerBar />

    <!-- Layout Debugger (Ctrl+Shift+D to toggle) -->
    <LayoutDebugger />

    <!-- Audio Debugger (Ctrl+Shift+A to toggle) -->
    <AudioDebugger />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { GreetService } from "../bindings/changeme/service"
import { Events } from "@wailsio/runtime"
import FluentNavigation from './components/FluentNavigation.vue'
import FluentCard from './components/FluentCard.vue'
import TitleBar from './components/TitleBar.vue'
import PlayerBar from './components/PlayerBar.vue'
import LayoutDebugger from './components/LayoutDebugger.vue'
import AudioDebugger from './components/AudioDebugger.vue'
import { useTheme } from './composables/useTheme.js'

// Theme management
const { initTheme } = useTheme()

// Reactive data
const name = ref('')
const result = ref('')
const timeDisplay = ref('Listening for Time event...')
const currentView = ref('home')

// Methods
const doGreet = async () => {
  try {
    const nameToGreet = name.value || 'anonymous'
    const greeting = await GreetService.Greet(nameToGreet)
    result.value = greeting
  } catch (err) {
    console.error('Error greeting:', err)
    result.value = 'Error occurred while greeting'
  }
}

const clearForm = () => {
  name.value = ''
  result.value = ''
}

const handleNavigation = (viewId) => {
  currentView.value = viewId
  // Clear form when navigating away from greet view
  if (viewId !== 'greet') {
    clearForm()
  }
}

const getViewTitle = () => {
  const titles = {
    home: 'Home',
    greet: 'Greet Service',
    media: 'Media Player',
    settings: 'Settings',
    about: 'About'
  }
  return titles[currentView.value] || 'Unknown'
}

// Event listener for time updates
let timeEventListener = null

onMounted(() => {
  // Initialize theme system
  initTheme()

  // Listen for time events from the backend
  timeEventListener = Events.On('time', (time) => {
    timeDisplay.value = time.data
  })
})

onUnmounted(() => {
  // Clean up event listener
  if (timeEventListener) {
    Events.Off('time', timeEventListener)
  }
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--fluent-neutral-background);
  overflow: hidden;
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 确保 flex 子元素可以收缩 */
  margin-bottom: 80px; /* 为播放器底栏留出空间 */
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--fluent-neutral-background);
}

.content-area {
  flex: 1;
  padding: var(--fluent-spacing-xxl);
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
}

.view-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 1600x900 optimizations */
@media (min-width: 1600px) {
  .view-container {
    max-width: 1400px;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--fluent-spacing-xl);
  }

  .content-area {
    padding: var(--fluent-spacing-xxl) var(--fluent-spacing-xl);
  }

  .page-header {
    margin-bottom: calc(var(--fluent-spacing-xxl) * 1.5);
  }

  .greeting-form {
    gap: var(--fluent-spacing-xl);
  }

  .form-actions {
    gap: var(--fluent-spacing-l);
  }
}

/* 确保无边框窗口的正确布局 */
.app-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

/* 调试样式 - 可以临时启用来检查布局 */
/*
.app-container { border: 2px solid red; }
.app-body { border: 2px solid blue; }
.fluent-navigation { border: 2px solid green; }
.main-content { border: 2px solid orange; }
.content-area { border: 2px solid purple; }
*/

.page-header {
  margin-bottom: var(--fluent-spacing-xxl);
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--fluent-spacing-l);
  margin-bottom: var(--fluent-spacing-xxl);
}

.greeting-form {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-l);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-s);
}

.form-label {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  color: var(--fluent-text-primary);
}

.form-actions {
  display: flex;
  gap: var(--fluent-spacing-m);
}

.result-section {
  margin-top: var(--fluent-spacing-l);
  padding-top: var(--fluent-spacing-l);
  border-top: 1px solid var(--fluent-border);
}

.result-label {
  font-size: var(--fluent-font-size-100);
  font-weight: 600;
  color: var(--fluent-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--fluent-spacing-s);
}

.result-text {
  font-size: var(--fluent-font-size-300);
  font-weight: 600;
  color: var(--fluent-primary);
  padding: var(--fluent-spacing-m);
  background-color: var(--fluent-card-background-secondary);
  border-radius: var(--fluent-corner-radius-medium);
  border-left: 3px solid var(--fluent-primary);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--fluent-spacing-s) 0;
  border-bottom: 1px solid var(--fluent-border);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: var(--fluent-text-secondary);
}

.info-value {
  color: var(--fluent-text-primary);
}

.fluent-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.fluent-button:disabled:hover {
  background-color: var(--fluent-primary);
  border-color: var(--fluent-primary);
}
</style>
