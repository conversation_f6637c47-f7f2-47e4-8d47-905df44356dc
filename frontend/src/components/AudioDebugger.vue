<template>
  <div class="audio-debugger" v-if="showDebugger">
    <div class="debug-panel">
      <h3>Audio Debugger</h3>
      <button @click="toggleDebugger" class="close-btn">×</button>
      
      <div class="debug-info">
        <div class="info-section">
          <h4>Audio State</h4>
          <p>State: {{ getAudioState() }}</p>
          <p>Can Play: {{ canPlay() ? 'Yes' : 'No' }}</p>
          <p>Is Playing: {{ isPlaying ? 'Yes' : 'No' }}</p>
          <p>Current Time: {{ formatTime(currentTime) }}</p>
          <p>Duration: {{ formatTime(duration) }}</p>
          <p>Volume: {{ (volume * 100).toFixed(0) }}%</p>
          <p>Muted: {{ isMuted ? 'Yes' : 'No' }}</p>
        </div>
        
        <div class="info-section">
          <h4>Current Track</h4>
          <p>Title: {{ currentTrack?.title || 'None' }}</p>
          <p>Artist: {{ currentTrack?.artist || 'None' }}</p>
          <p>URL: {{ currentTrack?.url || 'None' }}</p>
        </div>
        
        <div class="info-section">
          <h4>Playlist</h4>
          <p>Total Tracks: {{ playlist.length }}</p>
          <p>Current Index: {{ currentIndex }}</p>
          <p>Shuffle: {{ isShuffled ? 'On' : 'Off' }}</p>
          <p>Repeat: {{ repeatMode }}</p>
        </div>
      </div>
      
      <div class="debug-controls">
        <button @click="testAudioFile" class="debug-btn">
          Test Audio File
        </button>
        <button @click="forceReload" class="debug-btn">
          Force Reload
        </button>
        <button @click="checkBrowserSupport" class="debug-btn">
          Check Browser Support
        </button>
        <button @click="clearConsole" class="debug-btn">
          Clear Console
        </button>
      </div>
      
      <div class="debug-logs" v-if="logs.length > 0">
        <h4>Debug Logs</h4>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            :class="['log-entry', `log-${log.type}`]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { usePlayer } from '../composables/usePlayer.js'

const {
  isPlaying,
  currentTime,
  duration,
  volume,
  isMuted,
  isShuffled,
  repeatMode,
  currentTrack,
  playlist,
  currentIndex,
  formatTime,
  getAudioState,
  canPlay,
  loadTrack
} = usePlayer()

const showDebugger = ref(false)
const logs = ref([])

const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.push({ time, message, type })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value.shift()
  }
}

const toggleDebugger = () => {
  showDebugger.value = !showDebugger.value
}

const testAudioFile = async () => {
  if (!currentTrack.value) {
    addLog('No current track to test', 'error')
    return
  }
  
  addLog('Testing audio file: ' + currentTrack.value.url, 'info')
  
  try {
    const response = await fetch(currentTrack.value.url)
    if (response.ok) {
      const contentType = response.headers.get('content-type')
      const contentLength = response.headers.get('content-length')
      addLog(`File accessible: ${response.status} ${response.statusText}`, 'success')
      addLog(`Content-Type: ${contentType}`, 'info')
      addLog(`Content-Length: ${contentLength} bytes`, 'info')
    } else {
      addLog(`File not accessible: ${response.status} ${response.statusText}`, 'error')
    }
  } catch (error) {
    addLog(`Network error: ${error.message}`, 'error')
  }
}

const forceReload = () => {
  if (currentTrack.value) {
    addLog('Force reloading current track', 'info')
    loadTrack(currentTrack.value, currentIndex.value)
  } else {
    addLog('No track to reload', 'error')
  }
}

const checkBrowserSupport = () => {
  addLog('Checking browser audio support...', 'info')
  
  // 检查 Audio API 支持
  if (typeof Audio !== 'undefined') {
    addLog('HTML5 Audio API: Supported', 'success')
  } else {
    addLog('HTML5 Audio API: Not supported', 'error')
  }
  
  // 检查 Web Audio API 支持
  if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
    addLog('Web Audio API: Supported', 'success')
  } else {
    addLog('Web Audio API: Not supported', 'error')
  }
  
  // 检查 FLAC 支持
  const audio = new Audio()
  const canPlayFLAC = audio.canPlayType('audio/flac')
  addLog(`FLAC support: ${canPlayFLAC || 'Unknown'}`, canPlayFLAC ? 'success' : 'warning')
  
  // 检查其他格式
  const formats = {
    'MP3': 'audio/mpeg',
    'OGG': 'audio/ogg',
    'WAV': 'audio/wav',
    'AAC': 'audio/aac'
  }
  
  Object.entries(formats).forEach(([name, type]) => {
    const support = audio.canPlayType(type)
    addLog(`${name} support: ${support || 'No'}`, support ? 'success' : 'warning')
  })
}

const clearConsole = () => {
  logs.value = []
  console.clear()
  addLog('Console cleared', 'info')
}

const handleKeyPress = (event) => {
  // Press Ctrl+Shift+A to toggle audio debugger
  if (event.ctrlKey && event.shiftKey && event.key === 'A') {
    event.preventDefault()
    toggleDebugger()
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleKeyPress)
  
  // 监听控制台错误
  const originalError = console.error
  console.error = (...args) => {
    addLog(args.join(' '), 'error')
    originalError.apply(console, args)
  }
  
  // 监听控制台警告
  const originalWarn = console.warn
  console.warn = (...args) => {
    addLog(args.join(' '), 'warning')
    originalWarn.apply(console, args)
  }
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})

// 暴露全局函数
window.toggleAudioDebugger = toggleDebugger
</script>

<style scoped>
.audio-debugger {
  position: fixed;
  top: 50px;
  left: 20px;
  z-index: 10000;
  background: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  box-shadow: var(--fluent-shadow-16);
  padding: var(--fluent-spacing-l);
  min-width: 300px;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.debug-panel h3 {
  margin: 0 0 var(--fluent-spacing-m) 0;
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-300);
}

.close-btn {
  position: absolute;
  top: var(--fluent-spacing-s);
  right: var(--fluent-spacing-s);
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--fluent-text-secondary);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--fluent-corner-radius-small);
}

.close-btn:hover {
  background-color: var(--fluent-neutral-background-hover);
  color: var(--fluent-text-primary);
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-m);
  margin-bottom: var(--fluent-spacing-l);
}

.info-section {
  border-bottom: 1px solid var(--fluent-border);
  padding-bottom: var(--fluent-spacing-s);
}

.info-section:last-child {
  border-bottom: none;
}

.info-section h4 {
  margin: 0 0 var(--fluent-spacing-xs) 0;
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
}

.info-section p {
  margin: 2px 0;
  color: var(--fluent-text-secondary);
  font-size: var(--fluent-font-size-100);
  font-family: 'Courier New', monospace;
}

.debug-controls {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-s);
  margin-bottom: var(--fluent-spacing-l);
}

.debug-btn {
  padding: var(--fluent-spacing-s) var(--fluent-spacing-m);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-medium);
  background-color: var(--fluent-card-background);
  color: var(--fluent-text-primary);
  cursor: pointer;
  font-size: var(--fluent-font-size-100);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.debug-btn:hover {
  background-color: var(--fluent-neutral-background-hover);
  border-color: var(--fluent-border-hover);
}

.debug-logs {
  border-top: 1px solid var(--fluent-border);
  padding-top: var(--fluent-spacing-m);
}

.debug-logs h4 {
  margin: 0 0 var(--fluent-spacing-s) 0;
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: var(--fluent-neutral-background);
  border-radius: var(--fluent-corner-radius-small);
  padding: var(--fluent-spacing-s);
}

.log-entry {
  display: flex;
  gap: var(--fluent-spacing-s);
  margin-bottom: 2px;
  font-size: var(--fluent-font-size-100);
  font-family: 'Courier New', monospace;
}

.log-time {
  color: var(--fluent-text-secondary);
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-info .log-message { color: var(--fluent-text-primary); }
.log-success .log-message { color: #107c10; }
.log-warning .log-message { color: #ff8c00; }
.log-error .log-message { color: #d13438; }

.audio-debugger {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}
</style>
