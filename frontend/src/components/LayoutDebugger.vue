<template>
  <div class="layout-debugger" v-if="showDebugger">
    <div class="debug-panel">
      <h3>Layout Debugger</h3>
      <button @click="toggleDebugger" class="close-btn">×</button>
      
      <div class="debug-info">
        <div class="info-section">
          <h4>Window Dimensions</h4>
          <p>Width: {{ windowSize.width }}px</p>
          <p>Height: {{ windowSize.height }}px</p>
          <p>Ratio: {{ aspectRatio }}</p>
        </div>
        
        <div class="info-section">
          <h4>Viewport</h4>
          <p>VW: {{ viewportSize.width }}px</p>
          <p>VH: {{ viewportSize.height }}px</p>
        </div>
        
        <div class="info-section">
          <h4>App Container</h4>
          <p>Width: {{ appSize.width }}px</p>
          <p>Height: {{ appSize.height }}px</p>
        </div>
        
        <div class="info-section">
          <h4>Content Area</h4>
          <p>Width: {{ contentSize.width }}px</p>
          <p>Height: {{ contentSize.height }}px</p>
          <p>Scroll: {{ contentSize.scrollHeight }}px</p>
        </div>
      </div>
      
      <div class="debug-controls">
        <button @click="toggleBorders" class="debug-btn">
          {{ showBorders ? 'Hide' : 'Show' }} Borders
        </button>
        <button @click="refreshSizes" class="debug-btn">
          Refresh Sizes
        </button>
      </div>
    </div>
  </div>
  
  <!-- Debug borders overlay -->
  <div v-if="showBorders" class="debug-borders">
    <style>
      .app-container { border: 2px solid red !important; }
      .app-body { border: 2px solid blue !important; }
      .fluent-navigation { border: 2px solid green !important; }
      .main-content { border: 2px solid orange !important; }
      .content-area { border: 2px solid purple !important; }
      .title-bar { border: 2px solid cyan !important; }
    </style>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const showDebugger = ref(false)
const showBorders = ref(false)

const windowSize = ref({ width: 0, height: 0 })
const viewportSize = ref({ width: 0, height: 0 })
const appSize = ref({ width: 0, height: 0 })
const contentSize = ref({ width: 0, height: 0, scrollHeight: 0 })

const aspectRatio = computed(() => {
  if (windowSize.value.height === 0) return '0:0'
  const ratio = windowSize.value.width / windowSize.value.height
  return `${ratio.toFixed(2)}:1`
})

const toggleDebugger = () => {
  showDebugger.value = !showDebugger.value
  if (showDebugger.value) {
    refreshSizes()
  }
}

const toggleBorders = () => {
  showBorders.value = !showBorders.value
}

const refreshSizes = () => {
  // Window size
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }
  
  // Viewport size
  viewportSize.value = {
    width: document.documentElement.clientWidth,
    height: document.documentElement.clientHeight
  }
  
  // App container size
  const appContainer = document.querySelector('.app-container')
  if (appContainer) {
    const rect = appContainer.getBoundingClientRect()
    appSize.value = {
      width: rect.width,
      height: rect.height
    }
  }
  
  // Content area size
  const contentArea = document.querySelector('.content-area')
  if (contentArea) {
    const rect = contentArea.getBoundingClientRect()
    contentSize.value = {
      width: rect.width,
      height: rect.height,
      scrollHeight: contentArea.scrollHeight
    }
  }
}

const handleResize = () => {
  if (showDebugger.value) {
    refreshSizes()
  }
}

const handleKeyPress = (event) => {
  // Press Ctrl+Shift+D to toggle debugger
  if (event.ctrlKey && event.shiftKey && event.key === 'D') {
    event.preventDefault()
    toggleDebugger()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeyPress)
})

// Expose toggle function globally for console access
window.toggleLayoutDebugger = toggleDebugger
</script>

<style scoped>
.layout-debugger {
  position: fixed;
  top: 50px;
  right: 20px;
  z-index: 10000;
  background: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  box-shadow: var(--fluent-shadow-16);
  padding: var(--fluent-spacing-l);
  min-width: 250px;
  max-height: 80vh;
  overflow-y: auto;
}

.debug-panel h3 {
  margin: 0 0 var(--fluent-spacing-m) 0;
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-300);
}

.close-btn {
  position: absolute;
  top: var(--fluent-spacing-s);
  right: var(--fluent-spacing-s);
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--fluent-text-secondary);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--fluent-corner-radius-small);
}

.close-btn:hover {
  background-color: var(--fluent-neutral-background-hover);
  color: var(--fluent-text-primary);
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-m);
}

.info-section {
  border-bottom: 1px solid var(--fluent-border);
  padding-bottom: var(--fluent-spacing-s);
}

.info-section:last-child {
  border-bottom: none;
}

.info-section h4 {
  margin: 0 0 var(--fluent-spacing-xs) 0;
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
}

.info-section p {
  margin: 2px 0;
  color: var(--fluent-text-secondary);
  font-size: var(--fluent-font-size-100);
  font-family: 'Courier New', monospace;
}

.debug-controls {
  margin-top: var(--fluent-spacing-m);
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-s);
}

.debug-btn {
  padding: var(--fluent-spacing-s) var(--fluent-spacing-m);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-medium);
  background-color: var(--fluent-card-background);
  color: var(--fluent-text-primary);
  cursor: pointer;
  font-size: var(--fluent-font-size-100);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.debug-btn:hover {
  background-color: var(--fluent-neutral-background-hover);
  border-color: var(--fluent-border-hover);
}

/* Ensure debugger is always visible */
.layout-debugger {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}
</style>
