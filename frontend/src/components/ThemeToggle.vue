<template>
  <div class="theme-toggle-container">
    <!-- Quick Toggle Button -->
    <button 
      class="theme-toggle-button fluent-button fluent-button-secondary"
      @click="toggleTheme"
      :title="getThemeLabel()"
    >
      <span class="theme-icon" v-html="getThemeIcon()"></span>
    </button>
    
    <!-- Theme Selector Dropdown -->
    <div class="theme-selector" v-if="showSelector">
      <div class="selector-header">
        <span class="selector-title">Theme</span>
        <button 
          class="close-button"
          @click="showSelector = false"
        >
          <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
          </svg>
        </button>
      </div>
      
      <div class="theme-options">
        <div 
          v-for="option in themeOptions" 
          :key="option.value"
          class="theme-option"
          :class="{ active: themePreference === option.value }"
          @click="selectTheme(option.value)"
        >
          <span class="option-icon" v-html="option.icon"></span>
          <div class="option-content">
            <div class="option-title">{{ option.title }}</div>
            <div class="option-description">{{ option.description }}</div>
          </div>
          <div class="option-indicator" v-if="themePreference === option.value">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="theme-preview">
        <div class="preview-title">Preview</div>
        <div class="preview-container">
          <div class="preview-card" :class="{ 'preview-dark': isDarkMode }">
            <div class="preview-header">
              <div class="preview-title-bar"></div>
              <div class="preview-controls">
                <div class="preview-control"></div>
                <div class="preview-control"></div>
                <div class="preview-control"></div>
              </div>
            </div>
            <div class="preview-content">
              <div class="preview-nav">
                <div class="preview-nav-item active"></div>
                <div class="preview-nav-item"></div>
                <div class="preview-nav-item"></div>
              </div>
              <div class="preview-main">
                <div class="preview-card-item"></div>
                <div class="preview-card-item"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Backdrop -->
    <div 
      v-if="showSelector" 
      class="theme-backdrop"
      @click="showSelector = false"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const { 
  isDarkMode, 
  themePreference, 
  setTheme, 
  toggleTheme, 
  getThemeIcon, 
  getThemeLabel 
} = useTheme()

const showSelector = ref(false)

const themeOptions = [
  {
    value: 'system',
    title: 'Use system setting',
    description: 'Automatically switch between light and dark themes',
    icon: `<svg width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
             <path d="M1.5 0A1.5 1.5 0 0 0 0 1.5v7A1.5 1.5 0 0 0 1.5 10H6v1H1a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-5v-1h4.5A1.5 1.5 0 0 0 16 8.5v-7A1.5 1.5 0 0 0 14.5 0h-13Zm0 1h13a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5Z"/>
           </svg>`
  },
  {
    value: 'light',
    title: 'Light',
    description: 'Use light theme',
    icon: `<svg width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
             <path d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0z"/>
           </svg>`
  },
  {
    value: 'dark',
    title: 'Dark',
    description: 'Use dark theme',
    icon: `<svg width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
             <path d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.040-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278z"/>
           </svg>`
  }
]

const selectTheme = (theme) => {
  setTheme(theme)
  showSelector.value = false
}

// Toggle selector visibility
const toggleSelector = () => {
  showSelector.value = !showSelector.value
}

// Handle right-click for advanced options
const handleContextMenu = (event) => {
  event.preventDefault()
  showSelector.value = true
}
</script>

<style scoped>
.theme-toggle-container {
  position: relative;
}

.theme-toggle-button {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.theme-toggle-button:hover {
  background-color: var(--fluent-neutral-background-hover);
}

.theme-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--fluent-text-primary);
}

.theme-selector {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--fluent-spacing-s);
  width: 320px;
  background-color: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  box-shadow: var(--fluent-shadow-16);
  z-index: 1000;
  overflow: hidden;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--fluent-spacing-l);
  border-bottom: 1px solid var(--fluent-border);
}

.selector-title {
  font-size: var(--fluent-font-size-300);
  font-weight: 600;
  color: var(--fluent-text-primary);
}

.close-button {
  background: none;
  border: none;
  padding: var(--fluent-spacing-xs);
  border-radius: var(--fluent-corner-radius-small);
  cursor: pointer;
  color: var(--fluent-text-secondary);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.close-button:hover {
  background-color: var(--fluent-neutral-background-hover);
  color: var(--fluent-text-primary);
}

.theme-options {
  padding: var(--fluent-spacing-s);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
  padding: var(--fluent-spacing-m);
  border-radius: var(--fluent-corner-radius-medium);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  margin-bottom: var(--fluent-spacing-xs);
}

.theme-option:hover {
  background-color: var(--fluent-neutral-background-hover);
}

.theme-option.active {
  background-color: var(--fluent-primary);
  color: white;
}

.option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  margin-bottom: 2px;
}

.option-description {
  font-size: var(--fluent-font-size-100);
  opacity: 0.8;
}

.option-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.theme-preview {
  padding: var(--fluent-spacing-l);
  border-top: 1px solid var(--fluent-border);
}

.preview-title {
  font-size: var(--fluent-font-size-100);
  font-weight: 600;
  color: var(--fluent-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--fluent-spacing-s);
}

.preview-container {
  border-radius: var(--fluent-corner-radius-medium);
  overflow: hidden;
  border: 1px solid var(--fluent-border);
}

.preview-card {
  background-color: #f3f2f1;
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.preview-card.preview-dark {
  background-color: #201f1e;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

.preview-card.preview-dark .preview-header {
  background-color: rgba(41, 40, 39, 0.8);
}

.preview-title-bar {
  width: 60px;
  height: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.preview-controls {
  display: flex;
  gap: 2px;
}

.preview-control {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
}

.preview-content {
  display: flex;
  height: 40px;
}

.preview-nav {
  width: 60px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.preview-card.preview-dark .preview-nav {
  background-color: rgba(41, 40, 39, 0.9);
}

.preview-nav-item {
  height: 6px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 1px;
}

.preview-nav-item.active {
  background-color: #0078d4;
}

.preview-main {
  flex: 1;
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.preview-card-item {
  height: 14px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.preview-card.preview-dark .preview-card-item {
  background-color: rgba(41, 40, 39, 0.8);
  border-color: rgba(255, 255, 255, 0.05);
}

.theme-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: transparent;
}
</style>
