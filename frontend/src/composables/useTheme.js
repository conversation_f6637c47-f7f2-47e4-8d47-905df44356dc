import { ref, watch, onMounted } from 'vue'

// Theme state
const isDarkMode = ref(false)
const themePreference = ref('system') // 'light', 'dark', 'system'

// Theme management composable
export function useTheme() {
  // Get system theme preference
  const getSystemTheme = () => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  // Apply theme to document
  const applyTheme = (dark) => {
    const root = document.documentElement
    
    if (dark) {
      root.classList.add('dark-theme')
      root.classList.remove('light-theme')
    } else {
      root.classList.add('light-theme')
      root.classList.remove('dark-theme')
    }
    
    // Update CSS custom properties for dynamic theming
    updateThemeProperties(dark)
  }

  // Update CSS custom properties
  const updateThemeProperties = (dark) => {
    const root = document.documentElement
    
    if (dark) {
      // Dark theme colors
      root.style.setProperty('--fluent-neutral-background', '#201f1e')
      root.style.setProperty('--fluent-neutral-background-hover', '#292827')
      root.style.setProperty('--fluent-neutral-background-pressed', '#323130')
      root.style.setProperty('--fluent-card-background', '#292827')
      root.style.setProperty('--fluent-card-background-secondary', '#323130')
      root.style.setProperty('--fluent-text-primary', '#ffffff')
      root.style.setProperty('--fluent-text-secondary', '#d2d0ce')
      root.style.setProperty('--fluent-text-disabled', '#605e5c')
      root.style.setProperty('--fluent-border', '#323130')
      root.style.setProperty('--fluent-border-hover', '#484644')
    } else {
      // Light theme colors
      root.style.setProperty('--fluent-neutral-background', '#f3f2f1')
      root.style.setProperty('--fluent-neutral-background-hover', '#edebe9')
      root.style.setProperty('--fluent-neutral-background-pressed', '#e1dfdd')
      root.style.setProperty('--fluent-card-background', '#ffffff')
      root.style.setProperty('--fluent-card-background-secondary', '#faf9f8')
      root.style.setProperty('--fluent-text-primary', '#323130')
      root.style.setProperty('--fluent-text-secondary', '#605e5c')
      root.style.setProperty('--fluent-text-disabled', '#a19f9d')
      root.style.setProperty('--fluent-border', '#edebe9')
      root.style.setProperty('--fluent-border-hover', '#d2d0ce')
    }
  }

  // Set theme preference
  const setTheme = (preference) => {
    themePreference.value = preference
    localStorage.setItem('theme-preference', preference)
    
    let shouldBeDark = false
    
    switch (preference) {
      case 'dark':
        shouldBeDark = true
        break
      case 'light':
        shouldBeDark = false
        break
      case 'system':
      default:
        shouldBeDark = getSystemTheme()
        break
    }
    
    isDarkMode.value = shouldBeDark
    applyTheme(shouldBeDark)
  }

  // Toggle between light and dark
  const toggleTheme = () => {
    const newTheme = isDarkMode.value ? 'light' : 'dark'
    setTheme(newTheme)
  }

  // Initialize theme
  const initTheme = () => {
    // Get saved preference or default to system
    const savedPreference = localStorage.getItem('theme-preference') || 'system'
    setTheme(savedPreference)
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', (e) => {
      if (themePreference.value === 'system') {
        isDarkMode.value = e.matches
        applyTheme(e.matches)
      }
    })
  }

  // Get theme icon
  const getThemeIcon = () => {
    switch (themePreference.value) {
      case 'light':
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z"/>
                </svg>`
      case 'dark':
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278z"/>
                </svg>`
      case 'system':
      default:
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M1.5 0A1.5 1.5 0 0 0 0 1.5v7A1.5 1.5 0 0 0 1.5 10H6v1H1a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-5v-1h4.5A1.5 1.5 0 0 0 16 8.5v-7A1.5 1.5 0 0 0 14.5 0h-13Zm0 1h13a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5ZM12 12.5a.5.5 0 1 1 1 0 .5.5 0 0 1-1 0Zm2 0a.5.5 0 1 1 1 0 .5.5 0 0 1-1 0ZM1.5 12h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1ZM1 14.25a.25.25 0 0 1 .25-.25h5.5a.25.25 0 1 1 0 .5h-5.5a.25.25 0 0 1-.25-.25Z"/>
                </svg>`
    }
  }

  // Get theme label
  const getThemeLabel = () => {
    switch (themePreference.value) {
      case 'light':
        return 'Light Theme'
      case 'dark':
        return 'Dark Theme'
      case 'system':
      default:
        return 'System Theme'
    }
  }

  return {
    isDarkMode,
    themePreference,
    setTheme,
    toggleTheme,
    initTheme,
    getThemeIcon,
    getThemeLabel
  }
}
