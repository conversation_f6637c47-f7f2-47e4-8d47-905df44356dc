<template>
  <div class="app-container">
    <!-- Custom Title Bar -->
    <TitleBar />

    <div class="app-body">
      <FluentNavigation @navigate="handleNavigation" />

      <main class="main-content">
        <div class="content-area">
        <!-- Home View -->
        <div v-if="currentView === 'home'" class="view-container">
          <!-- Page Header -->
          <div class="page-header">
            <h1 class="page-title">首页</h1>
            <p class="page-subtitle">发现你喜欢的音乐</p>
          </div>

          <!-- Top Section with Personal FM and AI Recommendations -->
          <div class="top-section">
            <!-- Personal FM Card -->
            <div class="fm-card">
              <div class="fm-header">
                <div class="fm-icon">📻</div>
                <div class="fm-info">
                  <h3 class="fm-title">私人FM</h3>
                  <p class="fm-subtitle">专属于你的音乐电台</p>
                </div>
                <div class="fm-actions">
                  <button class="heart-btn">❤️ 红心</button>
                  <button class="alpha-btn">🔮 Alpha</button>
                </div>
              </div>

              <div class="now-playing">
                <div class="album-cover">
                  <div class="placeholder-cover">🎵</div>
                </div>
                <div class="track-info">
                  <h4 class="track-title">当你看着我的时候</h4>
                  <p class="track-artist">罗嘉豪 - 当你看着我的时候</p>
                </div>
                <div class="player-controls">
                  <button class="control-btn">❤️</button>
                  <button class="control-btn">👎</button>
                  <button class="control-btn play-btn">▶️</button>
                </div>
              </div>
            </div>

            <!-- AI Recommendations Card -->
            <div class="ai-card">
              <div class="ai-header">
                <div class="ai-icon">🤖</div>
                <div class="ai-info">
                  <h3 class="ai-title">AI推荐</h3>
                  <p class="ai-subtitle">智能推荐你的偏好</p>
                </div>
              </div>

              <div class="ai-content">
                <div class="ai-avatar">
                  <div class="avatar-placeholder">🎵</div>
                </div>
                <div class="ai-text">
                  <h4 class="ai-status">正在为您AI推荐音乐...</h4>
                  <p class="ai-description">点击开始智能AI推荐</p>
                </div>
                <div class="ai-controls">
                  <button class="control-btn">❤️</button>
                  <button class="control-btn">👎</button>
                  <button class="control-btn play-btn">▶️</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Daily Recommendations Section -->
          <div class="daily-section">
            <div class="section-header">
              <div class="section-icon">🎵</div>
              <div class="section-info">
                <h3 class="section-title">每日推荐</h3>
                <p class="section-subtitle">根据你的音乐品味推荐</p>
              </div>
              <button class="view-all-btn">查看全部</button>
            </div>

            <!-- Date Card and Song List -->
            <div class="daily-content">
              <div class="date-card">
                <div class="date-number">21</div>
                <div class="date-month">08</div>
                <button class="play-all-btn">▶️</button>
              </div>

              <div class="song-grid">
                <div class="song-item">
                  <div class="song-cover">
                    <div class="placeholder-cover">🎤</div>
                  </div>
                  <div class="song-details">
                    <h4 class="song-title">至爱当你没有我</h4>
                    <p class="song-artist">陈奕迅</p>
                  </div>
                  <div class="song-duration">3:09</div>
                </div>

                <div class="song-item">
                  <div class="song-cover">
                    <div class="placeholder-cover">🎸</div>
                  </div>
                  <div class="song-details">
                    <h4 class="song-title">风大雨大不怕</h4>
                    <p class="song-artist">文慧</p>
                  </div>
                  <div class="song-duration">3:15</div>
                </div>

                <div class="song-item">
                  <div class="song-cover">
                    <div class="placeholder-cover">🎹</div>
                  </div>
                  <div class="song-details">
                    <h4 class="song-title">自由流浪人</h4>
                    <p class="song-artist">文慧</p>
                  </div>
                  <div class="song-duration">3:59</div>
                </div>

                <div class="song-item">
                  <div class="song-cover">
                    <div class="placeholder-cover">🎺</div>
                  </div>
                  <div class="song-details">
                    <h4 class="song-title">可遇不可求</h4>
                    <p class="song-artist">文慧</p>
                  </div>
                  <div class="song-duration">3:31</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Greet Service View -->
        <div v-else-if="currentView === 'greet'" class="view-container">
          <div class="page-header">
            <h1 class="fluent-title-1">Greeting Service</h1>
            <p class="fluent-body">Test the backend service integration</p>
          </div>

          <FluentCard
            title="Send Greeting"
            subtitle="Enter your name to receive a personalized greeting"
            :elevated="true"
          >
            <div class="greeting-form">
              <div class="form-group">
                <label class="form-label">Your Name</label>
                <input
                  class="fluent-input"
                  v-model="name"
                  type="text"
                  autocomplete="off"
                  @keyup.enter="doGreet"
                  placeholder="Enter your name"
                />
              </div>

              <div class="form-actions">
                <button
                  class="fluent-button fluent-button-primary"
                  @click="doGreet"
                  :disabled="!name.trim()"
                >
                  Send Greeting
                </button>
                <button
                  class="fluent-button fluent-button-secondary"
                  @click="clearForm"
                >
                  Clear
                </button>
              </div>
            </div>

            <div v-if="result" class="result-section">
              <div class="result-label">Response:</div>
              <div class="result-text">{{ result }}</div>
            </div>
          </FluentCard>
        </div>

        <!-- Other Views -->
        <div v-else class="view-container">
          <div class="page-header">
            <h1 class="fluent-title-1">{{ getViewTitle() }}</h1>
            <p class="fluent-body">This section is under development</p>
          </div>

          <FluentCard title="Coming Soon" subtitle="This feature will be available in a future update">
            <p>We're working hard to bring you more features. Stay tuned!</p>
          </FluentCard>
        </div>
      </div>
    </main>
    </div>

    <!-- Player Bar -->
    <PlayerBar />

    <!-- Layout Debugger (Ctrl+Shift+D to toggle) -->
    <LayoutDebugger />

    <!-- Audio Debugger (Ctrl+Shift+A to toggle) -->
    <AudioDebugger />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { GreetService } from "../bindings/changeme/service"
import { Events } from "@wailsio/runtime"
import FluentNavigation from './components/FluentNavigation.vue'
import FluentCard from './components/FluentCard.vue'
import TitleBar from './components/TitleBar.vue'
import PlayerBar from './components/PlayerBar.vue'
import LayoutDebugger from './components/LayoutDebugger.vue'
import AudioDebugger from './components/AudioDebugger.vue'
import { useTheme } from './composables/useTheme.js'

// Theme management
const { initTheme } = useTheme()

// Reactive data
const name = ref('')
const result = ref('')
const timeDisplay = ref('Listening for Time event...')
const currentView = ref('home')

// Methods
const doGreet = async () => {
  try {
    const nameToGreet = name.value || 'anonymous'
    const greeting = await GreetService.Greet(nameToGreet)
    result.value = greeting
  } catch (err) {
    console.error('Error greeting:', err)
    result.value = 'Error occurred while greeting'
  }
}

const clearForm = () => {
  name.value = ''
  result.value = ''
}

const handleNavigation = (viewId) => {
  currentView.value = viewId
  // Clear form when navigating away from greet view
  if (viewId !== 'greet') {
    clearForm()
  }
}

const getViewTitle = () => {
  const titles = {
    home: 'Home',
    greet: 'Greet Service',
    media: 'Media Player',
    settings: 'Settings',
    about: 'About'
  }
  return titles[currentView.value] || 'Unknown'
}

// Event listener for time updates
let timeEventListener = null

onMounted(() => {
  // Initialize theme system
  initTheme()

  // Listen for time events from the backend
  timeEventListener = Events.On('time', (time) => {
    timeDisplay.value = time.data
  })
})

onUnmounted(() => {
  // Clean up event listener
  if (timeEventListener) {
    Events.Off('time', timeEventListener)
  }
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--fluent-neutral-background);
  overflow: hidden;
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 确保 flex 子元素可以收缩 */
  margin-bottom: 80px; /* 为播放器底栏留出空间 */
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--fluent-neutral-background);
}

.content-area {
  flex: 1;
  padding: var(--fluent-spacing-xxl);
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
}

.view-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 1600x900 optimizations */
@media (min-width: 1600px) {
  .view-container {
    max-width: 1400px;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--fluent-spacing-xl);
  }

  .content-area {
    padding: var(--fluent-spacing-xxl) var(--fluent-spacing-xl);
  }

  .page-header {
    margin-bottom: calc(var(--fluent-spacing-xxl) * 1.5);
  }

  .greeting-form {
    gap: var(--fluent-spacing-xl);
  }

  .form-actions {
    gap: var(--fluent-spacing-l);
  }
}

/* 确保无边框窗口的正确布局 */
.app-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

/* 调试样式 - 可以临时启用来检查布局 */
/*
.app-container { border: 2px solid red; }
.app-body { border: 2px solid blue; }
.fluent-navigation { border: 2px solid green; }
.main-content { border: 2px solid orange; }
.content-area { border: 2px solid purple; }
*/

/* Home Page Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--fluent-spacing-xl);
  padding: 0 var(--fluent-spacing-m);
}

.page-title {
  font-size: var(--fluent-font-size-600);
  font-weight: 600;
  color: var(--fluent-text-primary);
  margin: 0;
}

.page-subtitle {
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-text-secondary);
  margin: 0;
}

/* Top Section - FM and AI Cards */
.top-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--fluent-spacing-l);
  margin-bottom: var(--fluent-spacing-xxl);
}

.fm-card, .ai-card {
  background: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  padding: var(--fluent-spacing-l);
  box-shadow: var(--fluent-shadow-2);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.fm-card:hover, .ai-card:hover {
  box-shadow: var(--fluent-shadow-4);
  border-color: var(--fluent-border-hover);
}

.fm-header, .ai-header {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
  margin-bottom: var(--fluent-spacing-l);
}

.fm-icon, .ai-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--fluent-neutral-background-hover);
  border-radius: var(--fluent-corner-radius-medium);
}

.fm-info, .ai-info {
  flex: 1;
}

.fm-title, .ai-title {
  font-size: var(--fluent-font-size-400);
  font-weight: 600;
  color: var(--fluent-text-primary);
  margin: 0 0 var(--fluent-spacing-xs) 0;
}

.fm-subtitle, .ai-subtitle {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  margin: 0;
}

.fm-actions {
  display: flex;
  gap: var(--fluent-spacing-s);
}

.heart-btn, .alpha-btn {
  padding: var(--fluent-spacing-xs) var(--fluent-spacing-s);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-small);
  background: var(--fluent-card-background);
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-100);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.heart-btn {
  background: #ff4757;
  color: white;
  border-color: #ff4757;
}

.alpha-btn {
  background: #5f27cd;
  color: white;
  border-color: #5f27cd;
}

.heart-btn:hover, .alpha-btn:hover {
  opacity: 0.8;
}

/* Now Playing and AI Content */
.now-playing, .ai-content {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
}

.album-cover {
  width: 80px;
  height: 80px;
  border-radius: var(--fluent-corner-radius-medium);
  overflow: hidden;
  flex-shrink: 0;
}

.album-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-cover {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--fluent-primary) 0%, var(--fluent-primary-hover) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  border-radius: var(--fluent-corner-radius-medium);
}

.ai-avatar {
  width: 80px;
  height: 80px;
  border-radius: var(--fluent-corner-radius-medium);
  background: var(--fluent-neutral-background-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-placeholder {
  font-size: 32px;
}

.track-info, .ai-text {
  flex: 1;
}

.track-title, .ai-status {
  font-size: var(--fluent-font-size-300);
  font-weight: 600;
  color: var(--fluent-text-primary);
  margin: 0 0 var(--fluent-spacing-xs) 0;
}

.track-artist, .ai-description {
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-text-secondary);
  margin: 0;
}

.player-controls, .ai-controls {
  display: flex;
  gap: var(--fluent-spacing-s);
  align-items: center;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--fluent-neutral-background-hover);
  border-radius: var(--fluent-corner-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  font-size: 14px;
}

.control-btn:hover {
  background: var(--fluent-neutral-background-pressed);
}

.play-btn {
  background: var(--fluent-primary);
  color: white;
}

.play-btn:hover {
  background: var(--fluent-primary-hover);
}

/* Daily Recommendations Section */
.daily-section {
  background: var(--fluent-card-background);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-large);
  padding: var(--fluent-spacing-l);
  box-shadow: var(--fluent-shadow-2);
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
  margin-bottom: var(--fluent-spacing-l);
}

.section-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--fluent-neutral-background-hover);
  border-radius: var(--fluent-corner-radius-medium);
}

.section-info {
  flex: 1;
}

.section-title {
  font-size: var(--fluent-font-size-400);
  font-weight: 600;
  color: var(--fluent-text-primary);
  margin: 0 0 var(--fluent-spacing-xs) 0;
}

.section-subtitle {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  margin: 0;
}

.view-all-btn {
  padding: var(--fluent-spacing-xs) var(--fluent-spacing-m);
  border: 1px solid var(--fluent-border);
  border-radius: var(--fluent-corner-radius-small);
  background: var(--fluent-card-background);
  color: var(--fluent-text-primary);
  font-size: var(--fluent-font-size-100);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.view-all-btn:hover {
  background: var(--fluent-neutral-background-hover);
  border-color: var(--fluent-border-hover);
}

.greeting-form {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-l);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-spacing-s);
}

.form-label {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  color: var(--fluent-text-primary);
}

.form-actions {
  display: flex;
  gap: var(--fluent-spacing-m);
}

.result-section {
  margin-top: var(--fluent-spacing-l);
  padding-top: var(--fluent-spacing-l);
  border-top: 1px solid var(--fluent-border);
}

.result-label {
  font-size: var(--fluent-font-size-100);
  font-weight: 600;
  color: var(--fluent-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--fluent-spacing-s);
}

.result-text {
  font-size: var(--fluent-font-size-300);
  font-weight: 600;
  color: var(--fluent-primary);
  padding: var(--fluent-spacing-m);
  background-color: var(--fluent-card-background-secondary);
  border-radius: var(--fluent-corner-radius-medium);
  border-left: 3px solid var(--fluent-primary);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--fluent-spacing-s) 0;
  border-bottom: 1px solid var(--fluent-border);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: var(--fluent-text-secondary);
}

.info-value {
  color: var(--fluent-text-primary);
}

.fluent-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Daily Content - Date Card and Song List */
.daily-content {
  display: flex;
  gap: var(--fluent-spacing-l);
  align-items: flex-start;
}

.date-card {
  background: linear-gradient(135deg, #42e695 0%, #3bb78f 100%);
  border-radius: var(--fluent-corner-radius-large);
  padding: var(--fluent-spacing-l);
  color: white;
  text-align: center;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--fluent-spacing-s);
  flex-shrink: 0;
}

.date-number {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

.date-month {
  font-size: var(--fluent-font-size-200);
  font-weight: 500;
  opacity: 0.9;
}

.play-all-btn {
  width: 48px;
  height: 48px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  font-size: 20px;
  margin-top: var(--fluent-spacing-s);
}

.play-all-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.song-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--fluent-spacing-m);
}

.song-item {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
  padding: var(--fluent-spacing-s);
  border-radius: var(--fluent-corner-radius-medium);
  cursor: pointer;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.song-item:hover {
  background: var(--fluent-neutral-background-hover);
}

.song-cover {
  width: 60px;
  height: 60px;
  border-radius: var(--fluent-corner-radius-medium);
  overflow: hidden;
  flex-shrink: 0;
}

.song-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.song-cover .placeholder-cover {
  font-size: 24px;
}

.song-details {
  flex: 1;
  min-width: 0;
}

.song-title {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  color: var(--fluent-text-primary);
  margin: 0 0 var(--fluent-spacing-xs) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.song-artist {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.song-duration {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  font-weight: 500;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .top-section {
    grid-template-columns: 1fr;
  }

  .song-grid {
    grid-template-columns: 1fr;
  }

  .daily-content {
    flex-direction: column;
  }

  .date-card {
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--fluent-spacing-s);
  }

  .content-area {
    padding: var(--fluent-spacing-l);
  }

  .fm-card, .ai-card {
    padding: var(--fluent-spacing-m);
  }

  .now-playing, .ai-content {
    flex-direction: column;
    text-align: center;
  }

  .album-cover, .ai-avatar {
    width: 60px;
    height: 60px;
  }
}

.fluent-button:disabled:hover {
  background-color: var(--fluent-primary);
  border-color: var(--fluent-primary);
}
</style>
