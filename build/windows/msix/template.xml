<?xml version="1.0" encoding="utf-8"?>
<MsixPackagingToolTemplate
    xmlns="http://schemas.microsoft.com/msix/packaging/msixpackagingtool/template/2022">
    <Settings
        AllowTelemetry="false"
        ApplyACLsToPackageFiles="true"
        GenerateCommandLineFile="true"
        AllowPromptForPassword="false">
    </Settings>
    <Installer
        Path="gmplayer"
        Arguments=""
        InstallLocation="C:\Program Files\My Company\My Product">
    </Installer>
    <PackageInformation
        PackageName="My Product"
        PackageDisplayName="My Product"
        PublisherName="CN=My Company"
        PublisherDisplayName="My Company"
        Version="*******"
        PackageDescription="My Product Description">
        <Capabilities>
            <Capability Name="runFullTrust" />
            
        </Capabilities>
        <Applications>
            <Application
                Id="com.wails.gmplayer"
                Description="My Product Description"
                DisplayName="My Product"
                ExecutableName="gmplayer"
                EntryPoint="Windows.FullTrustApplication">
                
            </Application>
        </Applications>
        <Resources>
            <Resource Language="en-us" />
        </Resources>
        <Dependencies>
            <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
        </Dependencies>
        <Properties>
            <Framework>false</Framework>
            <DisplayName>My Product</DisplayName>
            <PublisherDisplayName>My Company</PublisherDisplayName>
            <Description>My Product Description</Description>
            <Logo>Assets\AppIcon.png</Logo>
        </Properties>
    </PackageInformation>
    <SaveLocation PackagePath="gmplayer.msix" />
    <PackageIntegrity>
        <CertificatePath></CertificatePath>
    </PackageIntegrity>
</MsixPackagingToolTemplate>
