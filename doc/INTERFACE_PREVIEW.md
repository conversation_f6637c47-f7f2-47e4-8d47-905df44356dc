# 🎨 Fluent Design 界面预览

## 整体布局

```
┌─────────────────────────────────────────────────────────────────┐
│ GMPlayer - Fluent Design Application                           │
├─────────────────┬───────────────────────────────────────────────┤
│                 │                                               │
│   📱 GMPlayer   │           Welcome to GMPlayer                 │
│                 │     A modern Wails application with          │
│   ━━━━━━━━━━━━━   │           Fluent Design                       │
│   Main          │                                               │
│   🏠 Home       │   ┌─────────────────┐  ┌─────────────────┐   │
│   👋 Greet      │   │   Quick Start   │  │   System Info   │   │
│   ▶️ Media      │   │                 │  │                 │   │
│                 │   │ Try out the     │  │ Framework:      │   │
│   Tools         │   │ greeting        │  │ Wails v3 +     │   │
│   ⚙️ Settings   │   │ service...      │  │ Vue 3           │   │
│   ℹ️ About      │   │                 │  │                 │   │
│                 │   │ [Get Started]   │  │ Design:         │   │
│   ━━━━━━━━━━━━━   │   └─────────────────┘  │ Fluent Design   │   │
│   👤 User       │                        │ System          │   │
│   Online        │                        │                 │   │
│                 │                        │ Status:         │   │
└─────────────────┴────────────────────────│ Mon, 19 Aug...  │───┤
                                           └─────────────────┘   │
                                                                 │
```

## 🎨 视觉特效展示

### 1. Reveal Effect (光效跟随)
```
鼠标悬停时的光效：

┌─────────────────────┐
│  ✨ Quick Start     │  ← 鼠标位置产生光晕效果
│                     │
│  Try out the        │
│  greeting service   │
│                     │
│  [Get Started] ✨   │  ← 按钮也有光效
└─────────────────────┘
```

### 2. Card Hover Effects (卡片悬停)
```
正常状态：
┌─────────────────┐
│   System Info   │  shadow: 2px
│                 │  border: #edebe9
│   Framework:    │
│   Wails v3      │
└─────────────────┘

悬停状态：
┌─────────────────┐
│   System Info   │  shadow: 4px ↑ 提升
│                 │  border: #d2d0ce ← 加深
│   Framework:    │  transform: translateY(-1px)
│   Wails v3      │
└─────────────────┘
```

### 3. Navigation States (导航状态)
```
未选中：
│   🏠 Home       │  background: transparent
│   👋 Greet      │  color: #323130

悬停：
│   🏠 Home       │  background: #edebe9
│ ▶ 👋 Greet      │  color: #323130

选中：
│   🏠 Home       │  background: #0078d4
│ ▶ 👋 Greet      │  color: white
```

## 🌈 颜色系统

### 浅色主题
```css
Background:     #f3f2f1  (中性背景)
Card:          #ffffff   (卡片背景)
Primary:       #0078d4   (主色调 - 微软蓝)
Text Primary:  #323130   (主要文字)
Text Secondary: #605e5c  (次要文字)
Border:        #edebe9   (边框)
```

### 深色主题
```css
Background:     #201f1e  (深色背景)
Card:          #292827   (深色卡片)
Primary:       #0078d4   (保持主色调)
Text Primary:  #ffffff   (白色文字)
Text Secondary: #d2d0ce  (浅灰文字)
Border:        #323130   (深色边框)
```

## 📱 响应式布局

### 桌面版 (>1200px)
```
┌─────────────────────────────────────────────────────────┐
│ Navigation (280px) │        Content Area (flex)        │
│                    │                                    │
│                    │  ┌─────────┐  ┌─────────┐         │
│                    │  │  Card   │  │  Card   │         │
│                    │  └─────────┘  └─────────┘         │
└─────────────────────────────────────────────────────────┘
```

### 平板版 (768px - 1200px)
```
┌─────────────────────────────────────────────────────────┐
│ Nav (240px) │           Content Area                    │
│             │                                           │
│             │  ┌─────────────────┐                     │
│             │  │      Card       │                     │
│             │  └─────────────────┘                     │
│             │  ┌─────────────────┐                     │
│             │  │      Card       │                     │
│             │  └─────────────────┘                     │
└─────────────────────────────────────────────────────────┘
```

## 🎯 交互动画

### 按钮点击动画
```
1. 正常状态:    [  Get Started  ]
2. 悬停状态:    [  Get Started  ] ← 背景变深
3. 按下状态:    [  Get Started  ] ← 轻微下沉
4. 释放状态:    [  Get Started  ] ← 回弹效果
```

### 卡片加载动画
```
1. 淡入: opacity: 0 → 1
2. 上滑: transform: translateY(20px) → translateY(0)
3. 时长: 200ms ease-out
```

### 页面切换动画
```
1. 当前页面淡出: opacity: 1 → 0
2. 新页面淡入:   opacity: 0 → 1
3. 内容上滑:     translateY(10px) → translateY(0)
```

## 🔧 组件状态

### FluentCard 组件
```
Props:
- title: "Quick Start"
- subtitle: "Get started with the application"
- hover: true (启用悬停效果)
- clickable: true (可点击)
- elevated: false (是否提升阴影)

States:
- Normal: 基础样式
- Hover: 提升 + 光效
- Active: 按下效果
- Focus: 焦点样式
```

### FluentButton 组件
```
Variants:
- Primary: 蓝色主按钮
- Secondary: 白色次按钮
- Disabled: 禁用状态

States:
- Rest: 静止状态
- Hover: 悬停加深
- Pressed: 按下变暗
- Disabled: 半透明
```

## 🎨 设计细节

### 圆角系统
```
Small:  2px  (小元素)
Medium: 4px  (按钮、输入框)
Large:  8px  (卡片)
```

### 阴影层级
```
Level 2:  0 1px 2px rgba(0,0,0,0.14)  (卡片默认)
Level 4:  0 2px 4px rgba(0,0,0,0.14)  (卡片悬停)
Level 8:  0 4px 8px rgba(0,0,0,0.14)  (提升卡片)
Level 16: 0 8px 16px rgba(0,0,0,0.14) (模态框)
```

### 字体层级
```
Title 1:  24px, 600 weight (页面标题)
Title 2:  20px, 600 weight (区块标题)
Title 3:  18px, 600 weight (卡片标题)
Body:     14px, 400 weight (正文)
Caption:  12px, 400 weight (说明文字)
```

---

**这个界面完美复现了 Windows 11 和 WinUI 3 的现代化设计语言！** ✨
