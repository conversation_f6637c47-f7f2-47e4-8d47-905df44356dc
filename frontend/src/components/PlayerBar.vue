<template>
  <div class="player-bar">
    <div class="player-content">
      <!-- 当前播放信息 -->
      <div class="track-info">
        <div class="track-artwork">
          <img
            :src="(currentTrack?.artwork) || '/wails.png'"
            :alt="(currentTrack?.title) || 'GMPlayer'"
            @error="handleArtworkError"
          />
        </div>
        <div class="track-details">
          <div class="track-title">{{ (currentTrack?.title) || '花妖' }}</div>
          <div class="track-artist">{{ (currentTrack?.artist) || '刀郎' }}</div>
        </div>
      </div>
      
      <!-- 播放控制 -->
      <div class="player-controls">
        <div class="control-buttons">
          <button 
            class="control-btn shuffle-btn"
            :class="{ active: isShuffled }"
            @click="toggleShuffle"
            title="Shuffle"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M0 3.5A.5.5 0 0 1 .5 3H1c2.202 0 3.827 1.24 4.874 2.418.49.552.865 1.102 1.126 1.532.26-.43.636-.98 1.126-1.532C9.173 4.24 10.798 3 13 3v1c-1.798 0-3.173 1.01-4.126 2.082A9.624 9.624 0 0 0 7.556 8a9.624 9.624 0 0 0 1.317 1.918C9.828 10.99 11.204 12 13 12v1c-2.202 0-3.827-1.24-4.874-2.418A10.595 10.595 0 0 1 7 9.05c-.26.43-.636.98-1.126 1.532C4.827 11.76 3.202 13 1 13H.5a.5.5 0 0 1 0-1H1c1.798 0 3.173-1.01 4.126-2.082A9.624 9.624 0 0 0 6.444 8a9.624 9.624 0 0 0-1.317-1.918C4.172 5.01 2.796 4 1 4H.5a.5.5 0 0 1-.5-.5z"/>
              <path d="M13 5.466V1.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384l-2.36 1.966a.25.25 0 0 1-.41-.192zm0 9v-3.932a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384l-2.36 1.966a.25.25 0 0 1-.41-.192z"/>
            </svg>
          </button>
          
          <button 
            class="control-btn prev-btn"
            @click="previousTrack"
            title="Previous"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M6.271 5.055a.5.5 0 0 1 .52.038L11 7.055a.5.5 0 0 1 0 .89L6.791 9.907a.5.5 0 0 1-.791-.389V5.482a.5.5 0 0 1 .271-.427z"/>
            </svg>
          </button>
          
          <button 
            class="control-btn play-btn"
            @click="togglePlayPause"
            :title="isPlaying ? 'Pause' : 'Play'"
          >
            <svg v-if="!isPlaying" width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
              <path d="m11.596 8.697-6.363 3.692c-.54.313-1.233-.066-1.233-.697V4.308c0-.63.692-1.01 1.233-.696l6.363 3.692a.802.802 0 0 1 0 1.393z"/>
            </svg>
            <svg v-else width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
              <path d="M5.5 3.5A1.5 1.5 0 0 1 7 2h2a1.5 1.5 0 0 1 1.5 1.5v9A1.5 1.5 0 0 1 9 14H7a1.5 1.5 0 0 1-1.5-1.5v-9z"/>
            </svg>
          </button>
          
          <button 
            class="control-btn next-btn"
            @click="nextTrack"
            title="Next"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M9.729 5.055a.5.5 0 0 0-.52.038L5 7.055a.5.5 0 0 0 0 .89l4.209 1.962a.5.5 0 0 0 .791-.389V5.482a.5.5 0 0 0-.271-.427z"/>
            </svg>
          </button>
          
          <button 
            class="control-btn repeat-btn"
            :class="{ active: repeatMode !== 'none' }"
            @click="toggleRepeat"
            :title="getRepeatTitle()"
          >
            <svg v-if="repeatMode === 'one'" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M11 5.466V4H5a4 4 0 0 0-3.584 5.777.5.5 0 1 1-.896.446A5 5 0 0 1 5 3h6V1.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384l-2.36 1.966A.25.25 0 0 1 11 5.466zm-6 4.534V8h6a4 4 0 0 1 3.584-5.777.5.5 0 1 1 .896.446A5 5 0 0 0 11 13H5v1.466a.25.25 0 0 1-.41.192l-2.36-1.966a.086.086 0 0 1 0-.384l2.36-1.966A.25.25 0 0 1 5 10.466z"/>
              <text x="8" y="11" text-anchor="middle" font-size="6" font-weight="bold">1</text>
            </svg>
            <svg v-else width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M11 5.466V4H5a4 4 0 0 0-3.584 5.777.5.5 0 1 1-.896.446A5 5 0 0 1 5 3h6V1.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384l-2.36 1.966A.25.25 0 0 1 11 5.466zm-6 4.534V8h6a4 4 0 0 1 3.584-5.777.5.5 0 1 1 .896.446A5 5 0 0 0 11 13H5v1.466a.25.25 0 0 1-.41.192l-2.36-1.966a.086.086 0 0 1 0-.384l2.36-1.966A.25.25 0 0 1 5 10.466z"/>
            </svg>
          </button>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-section">
          <span class="time-current">{{ formatTime(currentTime) }}</span>
          <div class="progress-container" @click="handleProgressClick">
            <div class="progress-track">
              <div 
                class="progress-fill"
                :style="{ width: progressPercentage + '%' }"
              ></div>
              <div 
                class="progress-handle"
                :style="{ left: progressPercentage + '%' }"
                @mousedown="startDrag"
              ></div>
            </div>
          </div>
          <span class="time-duration">{{ formatTime(duration) }}</span>
        </div>
      </div>
      
      <!-- 音量控制 -->
      <div class="volume-controls">
        <button
          class="fluent-button fluent-button-secondary volume-btn"
          @click="toggleMute"
          :title="isMuted ? 'Unmute' : 'Mute'"
        >
          <svg v-if="isMuted || volume === 0" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
            <path d="M6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06zm7.137 2.096a.5.5 0 0 1 0 .708L12.207 8l1.647 1.646a.5.5 0 0 1-.708.708L11.5 8.707l-1.646 1.647a.5.5 0 0 1-.708-.708L10.793 8 9.146 6.354a.5.5 0 1 1 .708-.708L11.5 7.293l1.646-1.647a.5.5 0 0 1 .708 0z"/>
          </svg>
          <svg v-else-if="volume < 0.3" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
            <path d="M9 4a.5.5 0 0 0-.812-.39L5.825 5.5H3.5A.5.5 0 0 0 3 6v4a.5.5 0 0 0 .5.5h2.325l2.363 1.89A.5.5 0 0 0 9 12V4zM6.312 6.39 8 5.04v5.92L6.312 9.61A.5.5 0 0 0 6 9.5H4v-3h2a.5.5 0 0 0 .312-.11zM12.025 8a4.486 4.486 0 0 1-1.318 3.182L10 10.475A3.489 3.489 0 0 0 11.025 8 3.49 3.49 0 0 0 10 5.525l.707-.707A4.486 4.486 0 0 1 12.025 8z"/>
          </svg>
          <svg v-else-if="volume < 0.7" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
            <path d="M9 4a.5.5 0 0 0-.812-.39L5.825 5.5H3.5A.5.5 0 0 0 3 6v4a.5.5 0 0 0 .5.5h2.325l2.363 1.89A.5.5 0 0 0 9 12V4zM6.312 6.39 8 5.04v5.92L6.312 9.61A.5.5 0 0 0 6 9.5H4v-3h2a.5.5 0 0 0 .312-.11zM12.025 8a4.486 4.486 0 0 1-1.318 3.182L10 10.475A3.489 3.489 0 0 0 11.025 8 3.49 3.49 0 0 0 10 5.525l.707-.707A4.486 4.486 0 0 1 12.025 8z"/>
          </svg>
          <svg v-else width="16" height="16" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
            <path d="M11.536 14.01A8.473 8.473 0 0 0 14.026 8a8.473 8.473 0 0 0-2.49-6.01l-.708.707A7.476 7.476 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303l.708.707z"/>
            <path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.483 5.483 0 0 1 11.025 8a5.483 5.483 0 0 1-1.61 3.89l.706.706z"/>
            <path d="M8.707 11.182A4.486 4.486 0 0 0 10.025 8a4.486 4.486 0 0 0-1.318-3.182L8 5.525A3.489 3.489 0 0 1 9.025 8 3.49 3.49 0 0 1 8 10.475l.707.707zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z"/>
          </svg>
        </button>
        
        <div class="volume-slider">
          <input 
            type="range"
            min="0"
            max="1"
            step="0.01"
            v-model="volume"
            @input="updateVolume($event.target.value)"
            class="volume-input"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { usePlayer } from '../composables/usePlayer.js'

// 使用播放器 composable
const {
  isPlaying,
  currentTime,
  duration,
  volume,
  isMuted,
  isShuffled,
  repeatMode,
  currentTrack,
  progressPercentage,
  togglePlayPause,
  previousTrack,
  nextTrack,
  toggleShuffle,
  toggleRepeat,
  toggleMute,
  updateVolume,
  seekTo,
  formatTime,
  getRepeatTitle,
  setPlaylist,
  cleanup
} = usePlayer()

// 拖拽状态
const isDragging = ref(false)

// 拖拽和点击相关的方法
const handleProgressClick = (event) => {
  if (isDragging.value) return

  const rect = event.currentTarget.getBoundingClientRect()
  const percentage = (event.clientX - rect.left) / rect.width
  const seekTime = percentage * duration.value

  seekTo(seekTime)
}

const startDrag = (event) => {
  isDragging.value = true
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', endDrag)
}

const handleDrag = (event) => {
  if (!isDragging.value) return

  const progressContainer = document.querySelector('.progress-container')
  const rect = progressContainer.getBoundingClientRect()
  const percentage = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width))
  const seekTime = percentage * duration.value

  currentTime.value = seekTime
}

const endDrag = () => {
  if (isDragging.value) {
    seekTo(currentTime.value)
  }
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', endDrag)
}

const handleArtworkError = (event) => {
  event.target.src = '/wails.png' // 使用默认图片
}

// 初始化
onMounted(() => {
  // 设置真实的播放列表
  const realPlaylist = [
    {
      id: 1,
      title: '花妖',
      artist: '刀郎',
      url: '/audio/刀郎 - 花妖.flac',
      artwork: '/wails.png' // 使用默认图片作为封面
    }
  ]
  setPlaylist(realPlaylist)
})

onUnmounted(() => {
  cleanup()
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', endDrag)
})
</script>

<style scoped>
.player-bar {
  height: 80px;
  background-color: var(--fluent-card-background);
  border-top: 1px solid var(--fluent-border);
  display: flex;
  align-items: center;
  padding: 0 var(--fluent-spacing-l);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

.player-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: var(--fluent-spacing-l);
}

/* 音轨信息 */
.track-info {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-m);
  min-width: 250px;
  max-width: 300px;
}

.track-artwork {
  width: 50px;
  height: 50px;
  border-radius: var(--fluent-corner-radius-medium);
  overflow: hidden;
  background-color: var(--fluent-neutral-background);
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-artwork img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.track-details {
  flex: 1;
  min-width: 0;
}

.track-title {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  color: var(--fluent-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.track-artist {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 播放控制 */
.player-controls {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--fluent-spacing-s);
  max-width: 600px;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-s);
}

.control-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--fluent-text-secondary);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.control-btn:hover {
  background-color: var(--fluent-neutral-background-hover);
  color: var(--fluent-text-primary);
}

.control-btn.active {
  color: var(--fluent-primary);
}

.play-btn {
  width: 44px;
  height: 44px;
  background-color: var(--fluent-primary);
  color: white;
}

.play-btn:hover {
  background-color: var(--fluent-primary-hover);
}

.play-btn:active {
  background-color: var(--fluent-primary-pressed);
}

/* 进度条 */
.progress-section {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-s);
  width: 100%;
  max-width: 500px;
}

.time-current,
.time-duration {
  font-size: var(--fluent-font-size-100);
  color: var(--fluent-text-secondary);
  font-family: 'Courier New', monospace;
  min-width: 35px;
  text-align: center;
}

.progress-container {
  flex: 1;
  height: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--fluent-spacing-xs) 0;
}

.progress-track {
  width: 100%;
  height: 4px;
  background-color: var(--fluent-neutral-background-hover);
  border-radius: 2px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background-color: var(--fluent-primary);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-handle {
  width: 12px;
  height: 12px;
  background-color: var(--fluent-primary);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  opacity: 0;
  transition: opacity var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.progress-container:hover .progress-handle {
  opacity: 1;
}

/* 音量控制 */
.volume-controls {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-s);
  min-width: 120px;
}

.volume-btn {
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-slider {
  width: 80px;
}

.volume-input {
  width: 100%;
  height: 4px;
  background: var(--fluent-neutral-background-hover);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-input::-webkit-slider-thumb {
  width: 12px;
  height: 12px;
  background: var(--fluent-primary);
  border-radius: 50%;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-input::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--fluent-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .track-info {
    min-width: 200px;
    max-width: 250px;
  }

  .progress-section {
    max-width: 400px;
  }
}

@media (max-width: 900px) {
  .player-content {
    gap: var(--fluent-spacing-m);
  }

  .track-info {
    min-width: 150px;
    max-width: 200px;
  }

  .control-buttons {
    gap: var(--fluent-spacing-xs);
  }

  .shuffle-btn,
  .repeat-btn {
    display: none;
  }
}

/* 深色主题调整 */
.dark-theme .player-bar {
  background-color: var(--fluent-card-background);
  border-top-color: var(--fluent-border);
}

/* 焦点样式 */
.control-btn:focus-visible {
  outline: 2px solid var(--fluent-primary);
  outline-offset: 2px;
}

.volume-input:focus-visible {
  outline: 2px solid var(--fluent-primary);
  outline-offset: 2px;
}

/* 动画效果 */
.track-artwork {
  transition: transform var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.track-artwork:hover {
  transform: scale(1.05);
}

/* 播放状态指示 */
.player-bar.playing .track-artwork::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 120, 212, 0.1) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
</style>
