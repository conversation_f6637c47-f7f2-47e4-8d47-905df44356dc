<template>
  <div class="title-bar" @mousedown="startDrag">
    <div class="title-bar-content">
      <!-- App Icon and Title -->
      <div class="title-section">
        <img src="/wails.png" class="app-icon" alt="GMPlayer" />
        <span class="app-title">GMPlayer</span>
      </div>
      
      <!-- Drag Area -->
      <div class="drag-area"></div>
      
      <!-- Window Controls -->
      <div class="window-controls">
        <!-- Theme Toggle -->
        <div class="control-group">
          <ThemeToggle />
        </div>
        
        <!-- Window Buttons -->
        <div class="window-buttons">
          <button 
            class="window-button minimize-button"
            @click="minimizeWindow"
            title="Minimize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <rect x="2" y="5.5" width="8" height="1" rx="0.5"/>
            </svg>
          </button>
          
          <button 
            class="window-button maximize-button"
            @click="toggleMaximize"
            :title="isMaximized ? 'Restore' : 'Maximize'"
          >
            <svg v-if="!isMaximized" width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <rect x="2" y="2" width="8" height="8" rx="0.5" fill="none" stroke="currentColor" stroke-width="1"/>
            </svg>
            <svg v-else width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <rect x="2.5" y="1.5" width="6" height="6" rx="0.5" fill="none" stroke="currentColor" stroke-width="1"/>
              <rect x="3.5" y="4.5" width="6" height="6" rx="0.5" fill="none" stroke="currentColor" stroke-width="1"/>
            </svg>
          </button>
          
          <button 
            class="window-button close-button"
            @click="closeWindow"
            title="Close"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path d="M2.22 2.22a.75.75 0 0 1 1.06 0L6 4.94l2.72-2.72a.75.75 0 1 1 1.06 1.06L7.06 6l2.72 2.72a.75.75 0 1 1-1.06 1.06L6 7.06 3.28 9.78a.75.75 0 0 1-1.06-1.06L4.94 6 2.22 3.28a.75.75 0 0 1 0-1.06z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import ThemeToggle from './ThemeToggle.vue'

const isMaximized = ref(false)
const isDragging = ref(false)

// Window control functions
const minimizeWindow = async () => {
  try {
    // Use Wails runtime to minimize window
    if (window.wails && window.wails.Window) {
      await window.wails.Window.Minimise()
    }
  } catch (error) {
    console.error('Failed to minimize window:', error)
  }
}

const toggleMaximize = async () => {
  try {
    if (window.wails && window.wails.Window) {
      if (isMaximized.value) {
        await window.wails.Window.Unmaximise()
      } else {
        await window.wails.Window.Maximise()
      }
      isMaximized.value = !isMaximized.value
    }
  } catch (error) {
    console.error('Failed to toggle maximize:', error)
  }
}

const closeWindow = async () => {
  try {
    if (window.wails && window.wails.Application) {
      await window.wails.Application.Quit()
    }
  } catch (error) {
    console.error('Failed to close window:', error)
  }
}

// Drag functionality
const startDrag = (event) => {
  // Only start drag if clicking on draggable areas
  const target = event.target
  const isDraggableArea = target.classList.contains('title-bar') || 
                         target.classList.contains('drag-area') ||
                         target.classList.contains('title-section') ||
                         target.classList.contains('app-title')
  
  if (!isDraggableArea) return
  
  isDragging.value = true
  
  try {
    if (window.wails && window.wails.Window) {
      window.wails.Window.StartDrag()
    }
  } catch (error) {
    console.error('Failed to start drag:', error)
  }
}

// Handle double-click to maximize/restore
const handleDoubleClick = (event) => {
  const target = event.target
  const isDraggableArea = target.classList.contains('title-bar') || 
                         target.classList.contains('drag-area') ||
                         target.classList.contains('title-section') ||
                         target.classList.contains('app-title')
  
  if (isDraggableArea) {
    toggleMaximize()
  }
}

// Listen for window state changes
const handleWindowStateChange = () => {
  // This would be called by Wails events if available
  // For now, we'll track state manually
}

onMounted(() => {
  // Add double-click listener
  document.addEventListener('dblclick', handleDoubleClick)
  
  // Listen for window state changes if Wails provides events
  if (window.wails && window.wails.Events) {
    // window.wails.Events.On('window:maximized', () => isMaximized.value = true)
    // window.wails.Events.On('window:unmaximized', () => isMaximized.value = false)
  }
})

onUnmounted(() => {
  document.removeEventListener('dblclick', handleDoubleClick)
})
</script>

<style scoped>
.title-bar {
  height: 40px;
  background-color: var(--fluent-neutral-background);
  border-bottom: 1px solid var(--fluent-border);
  display: flex;
  align-items: center;
  user-select: none;
  position: relative;
  z-index: 1000;
  -webkit-app-region: drag; /* Enable dragging on WebKit */
}

.title-bar-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 var(--fluent-spacing-s);
}

.title-section {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-s);
  padding: 0 var(--fluent-spacing-m);
  cursor: default;
}

.app-icon {
  width: 20px;
  height: 20px;
  border-radius: var(--fluent-corner-radius-small);
}

.app-title {
  font-size: var(--fluent-font-size-200);
  font-weight: 600;
  color: var(--fluent-text-primary);
  white-space: nowrap;
}

.drag-area {
  flex: 1;
  height: 100%;
  cursor: default;
}

.window-controls {
  display: flex;
  align-items: center;
  gap: var(--fluent-spacing-s);
  -webkit-app-region: no-drag; /* Disable dragging on controls */
}

.control-group {
  display: flex;
  align-items: center;
  margin-right: var(--fluent-spacing-s);
}

.window-buttons {
  display: flex;
  align-items: center;
}

.window-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: var(--fluent-corner-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--fluent-text-secondary);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  margin-left: 1px;
}

.window-button:hover {
  background-color: var(--fluent-neutral-background-hover);
  color: var(--fluent-text-primary);
}

.window-button:active {
  background-color: var(--fluent-neutral-background-pressed);
}

.close-button:hover {
  background-color: #e81123;
  color: white;
}

.close-button:active {
  background-color: #c50e1f;
  color: white;
}

.maximize-button:hover {
  background-color: var(--fluent-neutral-background-hover);
}

.minimize-button:hover {
  background-color: var(--fluent-neutral-background-hover);
}

/* macOS specific styles */
@media (platform: macos) {
  .title-bar {
    padding-left: 80px; /* Space for macOS traffic lights */
  }
}

/* Windows specific styles */
@media (platform: windows) {
  .window-buttons {
    margin-right: -8px; /* Align with window edge */
  }
  
  .window-button {
    width: 46px;
    height: 32px;
    border-radius: 0;
  }
}

/* Dark theme adjustments */
.dark-theme .title-bar {
  background-color: var(--fluent-neutral-background);
  border-bottom-color: var(--fluent-border);
}

/* Focus styles for accessibility */
.window-button:focus-visible {
  outline: 2px solid var(--fluent-primary);
  outline-offset: -2px;
}

/* Prevent text selection during drag */
.title-bar * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style>
