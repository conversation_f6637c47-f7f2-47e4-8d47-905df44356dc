# Feel free to remove those if you don't want/need to use them.
# Make sure to check the documentation at https://nfpm.goreleaser.com
#
# The lines below are called `modelines`. See `:help modeline`

name: "gmplayer"
arch: ${GOARCH}
platform: "linux"
version: "0.1.0"
section: "default"
priority: "extra"
maintainer: ${GIT_COMMITTER_NAME} <${GIT_COMMITTER_EMAIL}>
description: "My Product Description"
vendor: "My Company"
homepage: "https://wails.io"
license: "MIT"
release: "1"

contents:
  - src: "./bin/gmplayer"
    dst: "/usr/local/bin/gmplayer"
  - src: "./build/appicon.png"
    dst: "/usr/share/icons/hicolor/128x128/apps/gmplayer.png"
  - src: "./build/linux/gmplayer.desktop"
    dst: "/usr/share/applications/gmplayer.desktop"

# Default dependencies for Debian 12/Ubuntu 22.04+ with WebKit 4.1
depends:
  - libgtk-3-0
  - libwebkit2gtk-4.1-0

# Distribution-specific overrides for different package formats and WebKit versions
overrides:
  # RPM packages for RHEL/CentOS/AlmaLinux/Rocky Linux (WebKit 4.0)
  rpm:
    depends:
      - gtk3
      - webkit2gtk4.1
  
  # Arch Linux packages (WebKit 4.1)  
  archlinux:
    depends:
      - gtk3
      - webkit2gtk-4.1

# scripts section to ensure desktop database is updated after install
scripts:
  postinstall: "./build/linux/nfpm/scripts/postinstall.sh"
  # You can also add preremove, postremove if needed
  # preremove: "./build/linux/nfpm/scripts/preremove.sh"
  # postremove: "./build/linux/nfpm/scripts/postremove.sh"

# replaces:
#   - foobar
# provides:
#   - bar
# depends:
#   - gtk3
#   - libwebkit2gtk
# recommends:
#   - whatever
# suggests:
#   - something-else
# conflicts:
#   - not-foo
#   - not-bar
# changelog: "changelog.yaml"
