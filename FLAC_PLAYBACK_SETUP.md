# 🎵 FLAC 音乐文件播放设置

## ✨ 实现内容

### 🎶 **添加真实音乐文件**
- ✅ 将 `刀郎 - 花妖.flac` 移动到 `frontend/public/audio/` 目录
- ✅ 配置播放器底栏显示真实的音乐信息
- ✅ 设置播放器自动加载 FLAC 文件
- ✅ 确保播放器底栏始终可见

### 🔧 **技术修改**

#### 1. **文件位置调整**
```bash
# 创建音频目录
mkdir -p frontend/public/audio

# 复制 FLAC 文件到 public 目录
cp "刀郎 - 花妖.flac" frontend/public/audio/
```

#### 2. **播放列表配置**
```javascript
// PlayerBar.vue - 设置真实播放列表
const realPlaylist = [
  {
    id: 1,
    title: '花妖',
    artist: '刀郎',
    url: '/audio/刀郎 - 花妖.flac',
    artwork: '/wails.png' // 使用默认图片作为封面
  }
]
setPlaylist(realPlaylist)
```

#### 3. **播放器显示优化**
```vue
<!-- 移除条件显示，让播放器始终可见 -->
<div class="player-bar">  <!-- 原来是 v-if="currentTrack" -->
  
<!-- 添加默认显示信息 -->
<div class="track-title">{{ (currentTrack?.title) || '花妖' }}</div>
<div class="track-artist">{{ (currentTrack?.artist) || '刀郎' }}</div>
```

#### 4. **自动加载音轨**
```javascript
// usePlayer.js - 设置播放列表时自动加载第一首
const setPlaylist = (tracks) => {
  playlist.value = tracks
  currentIndex.value = 0
  // 自动加载第一首歌（但不播放）
  if (tracks.length > 0) {
    loadTrack(tracks[0], 0)
  }
}
```

## 🎨 界面效果

### 播放器底栏显示
```
┌─────────────────────────────────────────────────────────────────┐
│ [🎵] 花妖              [🔀][⏮️][⏯️][⏭️][🔁]  0:00 ━━━━━━━━ 0:00  [🔊]▬● │
│     刀郎                                                         │
└─────────────────────────────────────────────────────────────────┘
```

### 功能状态
- **音轨信息**：显示 "花妖 - 刀郎"
- **封面图片**：使用 Wails 默认图标
- **播放按钮**：可点击开始播放
- **进度条**：显示播放进度
- **音量控制**：可调节音量

## 🎵 FLAC 格式支持

### Howler.js FLAC 支持
```javascript
// Howler.js 原生支持 FLAC 格式
const sound = new Howl({
  src: ['/audio/刀郎 - 花妖.flac'],  // 直接支持 FLAC
  volume: 0.7,
  onload: () => {
    console.log('FLAC 文件加载成功')
    duration.value = sound.duration()
  },
  onloaderror: (id, error) => {
    console.error('FLAC 文件加载失败:', error)
  }
})
```

### 浏览器兼容性
- **Chrome/Edge**: ✅ 完全支持 FLAC
- **Firefox**: ✅ 支持 FLAC (v51+)
- **Safari**: ⚠️ 部分支持 (需要 macOS 11+)

## 🚀 使用方法

### 1. **启动应用**
```bash
# 构建应用
wails3 build

# 运行应用
./bin/gmplayer
```

### 2. **播放音乐**
1. 应用启动后，底部会显示播放器栏
2. 显示音轨信息："花妖 - 刀郎"
3. 点击播放按钮 ▶️ 开始播放
4. 使用进度条控制播放位置
5. 调节音量滑块控制音量

### 3. **播放控制**
- **播放/暂停**: 点击中央的播放按钮
- **进度控制**: 点击或拖拽进度条
- **音量调节**: 使用右侧音量滑块
- **静音**: 点击音量图标

## 📁 文件结构

```
gmplayer/
├── 刀郎 - 花妖.flac                    # 原始文件
└── frontend/
    └── public/
        └── audio/
            └── 刀郎 - 花妖.flac        # Web 可访问的文件
```

## 🔧 技术细节

### 文件访问路径
```javascript
// 在代码中的引用路径
url: '/audio/刀郎 - 花妖.flac'

// 实际文件位置
frontend/public/audio/刀郎 - 花妖.flac

// 浏览器访问路径
http://localhost:port/audio/刀郎 - 花妖.flac
```

### 音频加载流程
1. **应用启动** → 设置播放列表
2. **加载音轨** → 创建 Howl 实例
3. **文件加载** → 获取音频时长
4. **准备播放** → 等待用户点击播放
5. **开始播放** → 音频播放 + 进度更新

### 错误处理
```javascript
// 如果 FLAC 文件加载失败
onloaderror: (id, error) => {
  console.error('音频加载失败:', error)
  // 可以尝试其他格式或显示错误信息
}

// 封面图片加载失败
const handleArtworkError = (event) => {
  event.target.src = '/wails.png' // 使用默认图片
}
```

## 🎯 功能验证

### 测试清单
- [ ] 应用启动后播放器底栏可见
- [ ] 显示正确的音轨信息（花妖 - 刀郎）
- [ ] 点击播放按钮能开始播放
- [ ] 进度条显示播放进度
- [ ] 音量控制正常工作
- [ ] 播放完成后停止

### 调试方法
```javascript
// 在浏览器控制台检查
console.log('当前音轨:', currentTrack.value)
console.log('播放状态:', isPlaying.value)
console.log('音频时长:', duration.value)

// 检查文件是否可访问
fetch('/audio/刀郎 - 花妖.flac')
  .then(response => console.log('文件可访问:', response.ok))
  .catch(error => console.error('文件访问失败:', error))
```

## 🎉 完成状态

现在您的 GMPlayer 应用已经：

✅ **集成真实音乐文件**：刀郎的《花妖》FLAC 格式  
✅ **播放器底栏可见**：始终显示在应用底部  
✅ **音轨信息显示**：显示歌曲名和艺术家  
✅ **播放功能就绪**：点击播放按钮即可播放音乐  
✅ **完整控制界面**：进度条、音量控制等功能齐全  

您现在可以运行应用并享受高质量的 FLAC 音乐播放体验！🎵
